import { z } from "zod";

// Enums matching Prisma schema
export const AssignmentType = {
  ESSAY: "ESSAY",
  RESEARCH_PAPER: "RESEARCH_PAPER",
  TERM_PAPER: "TERM_PAPER",
  DISSERTATION: "DISSERTATION",
  THESIS: "THESIS",
  BOOK_REVIEW: "BOOK_REVIEW",
  ARTICLE_REVIEW: "ARTICLE_REVIEW",
  CASE_STUDY: "CASE_STUDY",
  DISCUSSION: "DISCUSSION",
  LAB_REPORT: "LAB_REPORT",
  LITERATURE_REVIEW: "LITERATURE_REVIEW",
  PERSONAL_STATEMENT: "PERSONAL_STATEMENT",
  REFLECTION_PAPER: "REFLECTION_PAPER",
  OTHER: "OTHER",
} as const;

export const Priority = {
  LOW: "LOW",
  MEDIUM: "MEDIUM",
  HIGH: "HIGH",
} as const;

export const AcademicLevel = {
  HIGH_SCHOOL: "HIGH_SCHOOL",
  UNDERGRADUATE: "UNDERGRADUATE",
  MASTERS: "MASTERS",
  PHD: "PHD",
  PROFESSIONAL: "PROFESSIONAL",
} as const;

export const Spacing = {
  SINGLE: "SINGLE",
  DOUBLE: "DOUBLE",
} as const;

export const LanguageStyle = {
  ENGLISH_US: "ENGLISH_US",
  ENGLISH_UK: "ENGLISH_UK",
  ENGLISH_AU: "ENGLISH_AU",
  OTHER: "OTHER",
} as const;

export const FormatStyle = {
  APA: "APA",
  MLA: "MLA",
  CHICAGO: "CHICAGO",
  HARVARD: "HARVARD",
  IEEE: "IEEE",
  OTHER: "OTHER",
} as const;

// Form validation schema
export const createOrderSchema = z.object({
  title: z.string().min(1, "Title is required").max(200, "Title is too long"),
  description: z.string().min(10, "Description must be at least 10 characters").max(2000, "Description is too long"),
  assignmentType: z.enum([
    "ESSAY",
    "RESEARCH_PAPER",
    "TERM_PAPER",
    "DISSERTATION",
    "THESIS",
    "BOOK_REVIEW",
    "ARTICLE_REVIEW",
    "CASE_STUDY",
    "DISCUSSION",
    "LAB_REPORT",
    "LITERATURE_REVIEW",
    "PERSONAL_STATEMENT",
    "REFLECTION_PAPER",
    "OTHER",
  ]),
  subject: z.string().min(1, "Subject is required").max(100, "Subject is too long"),
  service: z.string().min(1, "Service is required"),
  pageCount: z.number().min(1, "Page count must be at least 1").max(500, "Page count cannot exceed 500"),
  priority: z.enum(["LOW", "MEDIUM", "HIGH"]),
  academicLevel: z.enum([
    "HIGH_SCHOOL",
    "UNDERGRADUATE",
    "MASTERS",
    "PHD",
    "PROFESSIONAL",
  ]),
  spacing: z.enum(["SINGLE", "DOUBLE"]),
  languageStyle: z.enum(["ENGLISH_US", "ENGLISH_UK", "ENGLISH_AU", "OTHER"]),
  formatStyle: z.enum(["APA", "MLA", "CHICAGO", "HARVARD", "IEEE", "OTHER"]),
  numSources: z.number().min(0, "Number of sources cannot be negative").max(200, "Too many sources"),
  guidelines: z.string().optional(),
  estTime: z.date({
    required_error: "Estimated completion time is required",
  }),
});

export type CreateOrderForm = z.infer<typeof createOrderSchema>;

// Order options for UI
export const assignmentTypeOptions = [
  { label: "Essay", value: "ESSAY", description: "Academic essays and compositions" },
  { label: "Research Paper", value: "RESEARCH_PAPER", description: "In-depth research and analysis" },
  { label: "Term Paper", value: "TERM_PAPER", description: "Semester-long research projects" },
  { label: "Dissertation", value: "DISSERTATION", description: "Doctoral-level research" },
  { label: "Thesis", value: "THESIS", description: "Master's level research" },
  { label: "Book Review", value: "BOOK_REVIEW", description: "Critical analysis of books" },
  { label: "Article Review", value: "ARTICLE_REVIEW", description: "Academic article analysis" },
  { label: "Case Study", value: "CASE_STUDY", description: "Real-world scenario analysis" },
  { label: "Discussion", value: "DISCUSSION", description: "Forum posts and discussions" },
  { label: "Lab Report", value: "LAB_REPORT", description: "Scientific experiment reports" },
  { label: "Literature Review", value: "LITERATURE_REVIEW", description: "Comprehensive literature analysis" },
  { label: "Personal Statement", value: "PERSONAL_STATEMENT", description: "Application essays" },
  { label: "Reflection Paper", value: "REFLECTION_PAPER", description: "Personal reflection essays" },
  { label: "Other", value: "OTHER", description: "Custom assignment type" },
];

export const priorityOptions = [
  { value: "LOW", label: "Standard", description: "7+ days delivery", multiplier: 1.0, color: "bg-green-100 text-green-800" },
  { value: "MEDIUM", label: "Priority", description: "3-6 days delivery", multiplier: 1.5, color: "bg-yellow-100 text-yellow-800" },
  { value: "HIGH", label: "Urgent", description: "1-2 days delivery", multiplier: 2.0, color: "bg-red-100 text-red-800" },
] as const;

export const academicLevelOptions = [
  { value: "HIGH_SCHOOL", label: "High School", multiplier: 1.0, description: "Grade 9-12 level" },
  { value: "UNDERGRADUATE", label: "Undergraduate", multiplier: 1.2, description: "Bachelor's degree level" },
  { value: "MASTERS", label: "Masters", multiplier: 1.5, description: "Master's degree level" },
  { value: "PHD", label: "PhD", multiplier: 2.0, description: "Doctoral level" },
  { value: "PROFESSIONAL", label: "Professional", multiplier: 1.8, description: "Professional certification" },
] as const;

export const spacingOptions = [
  { value: "DOUBLE", label: "Double Spaced", description: "Standard spacing", multiplier: 1.0 },
  { value: "SINGLE", label: "Single Spaced", description: "Twice the content", multiplier: 2.0 },
] as const;

export const languageStyleOptions = [
  { value: "ENGLISH_US", label: "English (US)" },
  { value: "ENGLISH_UK", label: "English (UK)" },
  { value: "ENGLISH_AU", label: "English (AU)" },
  { value: "OTHER", label: "Other" },
] as const;

export const formatStyleOptions = [
  { value: "APA", label: "APA" },
  { value: "MLA", label: "MLA" },
  { value: "CHICAGO", label: "Chicago" },
  { value: "HARVARD", label: "Harvard" },
  { value: "IEEE", label: "IEEE" },
  { value: "OTHER", label: "Other" },
] as const;

// Order session storage interface
export interface OrderSessionData extends CreateOrderForm {
  uploadedFiles: Array<{
    id: string;
    name: string;
    url: string;
    size: number;
    type: string;
  }>;
  calculatedPrice: number;
  timestamp: number;
}

// How it works steps
export const howItWorksSteps = [
  {
    step: 1,
    title: "Create Order",
    description: "Fill out our detailed order form with your assignment requirements",
    icon: "create",
  },
  {
    step: 2,
    title: "Writer Assignment",
    description: "We assign a qualified writer who specializes in your subject area",
    icon: "assign",
  },
  {
    step: 3,
    title: "Writing Process",
    description: "Your writer works on your assignment following all guidelines",
    icon: "write",
  },
  {
    step: 4,
    title: "Order Completion",
    description: "Receive your completed assignment and request revisions if needed",
    icon: "complete",
  },
] as const;

// Pricing configuration - will be replaced by database-driven pricing
export const PRICING_CONFIG = {
  BASE_PRICE_PER_PAGE: 15, // Base price per page in USD
  MINIMUM_PRICE: 10,
  RUSH_MULTIPLIERS: {
    LOW: 1.0,      // Standard delivery (7+ days)
    MEDIUM: 1.5,   // Priority delivery (3-6 days)
    HIGH: 2.0,     // Urgent delivery (1-2 days)
  },
  ACADEMIC_LEVEL_MULTIPLIERS: {
    HIGH_SCHOOL: 1.0,
    UNDERGRADUATE: 1.2,
    MASTERS: 1.5,
    PHD: 2.0,
    PROFESSIONAL: 1.8,
  },
  SPACING_MULTIPLIERS: {
    DOUBLE: 1.0,   // Default - double spacing (base price)
    SINGLE: 2.0,   // Single spacing has twice the content
  },
} as const;
