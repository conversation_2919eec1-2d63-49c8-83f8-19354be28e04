"use client";

import { usePageViews } from "@/hooks/use-page-views";
import { Badge } from "@/components/ui/badge";
import { Eye, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface PageViewsTrackerProps {
  blogSlug: string;
}

export function PageViewsTracker({ blogSlug }: PageViewsTrackerProps) {
  const { pageViews, isAdmin, loading, error, refreshPageViews } = usePageViews(blogSlug);

  // Don't render anything if not admin
  if (!isAdmin) {
    return null;
  }

  if (loading) {
    return (
      <Card className="mt-8">
        <CardContent className="p-4">
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-4 w-24" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="mt-8 border-destructive/50">
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-destructive">
            <Eye className="h-4 w-4" />
            <span className="text-sm">Error loading page views</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mt-8 bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Eye className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
              Admin View: This post has been viewed
            </span>
            <Badge variant="secondary" className="bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-200">
              {pageViews.toLocaleString()} times
            </Badge>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={refreshPageViews}
            className="h-8 w-8 p-0 text-blue-600 hover:text-blue-800 hover:bg-blue-100 dark:text-blue-400 dark:hover:text-blue-200 dark:hover:bg-blue-900"
          >
            <RefreshCw className="h-3 w-3" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

// Separate component for just tracking (no display)
export function PageViewsOnlyTracker({ blogSlug }: PageViewsTrackerProps) {
  usePageViews(blogSlug);
  return null;
}
