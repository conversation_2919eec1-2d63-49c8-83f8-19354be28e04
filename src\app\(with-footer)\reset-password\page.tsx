"use client";

import { Suspense } from "react";
import { ResetPasswordForm } from "@/components/reset-password-form";
import { Card, CardContent } from "@/components/ui/card";
import { Loader2 } from "lucide-react";

function ResetPasswordContent() {
  return (
    <div className="container flex h-screen w-full items-center justify-center px-4 py-8">
      <ResetPasswordForm className="w-full max-w-md" />
    </div>
  );
}

export default function ResetPasswordPage() {
  return (
    <Suspense fallback={
      <div className="container flex h-screen w-full items-center justify-center px-4 py-8">
        <Card className="w-full max-w-md">
          <CardContent className="text-center py-8">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading...</p>
          </CardContent>
        </Card>
      </div>
    }>
      <ResetPasswordContent />
    </Suspense>
  );
}
