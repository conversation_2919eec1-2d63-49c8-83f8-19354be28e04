import { Metadata } from "next";
import { getRouteMetadata } from "@/lib/route-metadata";
import { ClientRegistrationForm } from "@/components/client-registration-form"

export async function generateMetadata(): Promise<Metadata> {
  return await getRouteMetadata("/register/client");
}

export default function ClientRegistrationPage() {
  return (
    <div className="container flex h-fit w-full items-center justify-center px-4 py-8">
      <ClientRegistrationForm className="w-full max-w-4xl" />
    </div>
  )
}

// TODO add captach check