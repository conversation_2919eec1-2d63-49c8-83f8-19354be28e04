import { NextRequest } from 'next/server';
import prisma from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { apiError, apiSuccess } from '@/lib/api-utils';

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authConfig);
    if (!session?.user) {
      return apiError('Unauthorized', 401);
    }

    const { id: assignmentId } = await params;

    // Verify assignment exists and user has access
    const assignment = await prisma.assignment.findUnique({
      where: { id: assignmentId },
      include: {
        client: true,
        assignedWriter: true,
        fileAttachments: {
          orderBy: { uploadedAt: 'desc' }
        }
      }
    });

    if (!assignment) {
      return apiError('Assignment not found', 404);
    }

    // Check if user has permission to view files for this assignment
    const isAuthorized =
      session.user.role === 'ADMIN' ||
      assignment.clientId === session.user.id ||
      assignment.assignedWriterId === session.user.id;

    if (!isAuthorized) {
      return apiError('Unauthorized to view files for this assignment', 403);
    }

    // Transform file attachments to match frontend expectations
    const files = assignment.fileAttachments.map(file => ({
      id: file.id,
      name: file.originalName,
      url: file.fileUrl,
      size: file.fileSize,
      type: file.fileType,
      uploadedAt: file.uploadedAt.toISOString()
    }));

    return apiSuccess(files, 'Files retrieved successfully');

  } catch (error) {
    console.error('Get files error:', error);
    return apiError('Internal server error', 500);
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authConfig);
    if (!session?.user) {
      return apiError('Unauthorized', 401);
    }

    const { id: assignmentId } = await params;
    const { searchParams } = new URL(request.url);
    const fileId = searchParams.get('fileId');

    if (!fileId) {
      return apiError('File ID is required', 400);
    }

    // Verify assignment exists and user has access
    const assignment = await prisma.assignment.findUnique({
      where: { id: assignmentId },
      include: { client: true, assignedWriter: true }
    });

    if (!assignment) {
      return apiError('Assignment not found', 404);
    }

    // Check if user has permission to delete files for this assignment
    const isAuthorized =
      session.user.role === 'ADMIN' ||
      assignment.clientId === session.user.id ||
      assignment.assignedWriterId === session.user.id;

    if (!isAuthorized) {
      return apiError('Unauthorized to delete files for this assignment', 403);
    }

    // Find and delete the file attachment
    const fileAttachment = await prisma.fileAttachment.findFirst({
      where: {
        id: fileId,
        assignmentId: assignmentId
      }
    });

    if (!fileAttachment) {
      return apiError('File not found', 404);
    }

    // Delete from database
    await prisma.fileAttachment.delete({
      where: { id: fileId }
    });

    // Note: We're not deleting from Supabase storage here to avoid data loss
    // You might want to implement a cleanup job later

    return apiSuccess(null, 'File deleted successfully');

  } catch (error) {
    console.error('Delete file error:', error);
    return apiError('Internal server error', 500);
  }
}
