import { NextRequest, NextResponse } from "next/server";
import  prisma  from "@/lib/prisma";
import { newsletterUnsubscribeSchema } from "@/lib/validations";
import { parseRequestBody, apiSuccess, apiError } from "@/lib/api-utils";
import type { NewsletterUnsubscribeRequest, NewsletterResponse } from "@/types/api";

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Parse and validate request body
    const body = await parseRequestBody(req, newsletterUnsubscribeSchema);
    if ('success' in body && !body.success) {
      return apiError(body.message, 400, body.errors);
    }

    const { email } = body as NewsletterUnsubscribeRequest;

    // Find and update subscription
    const subscription = await prisma.newsletterSubscription.findUnique({
      where: { email }
    });

    if (!subscription) {
      return apiError("Email not found in our newsletter list", 404);
    }

    if (!subscription.isActive) {
      return apiError("Email is already unsubscribed", 409);
    }

    // Update subscription to inactive
    const updatedSubscription = await prisma.newsletterSubscription.update({
      where: { email },
      data: {
        isActive: false,
        unsubscribedAt: new Date()
      }
    });

    return apiSuccess(updatedSubscription, "Successfully unsubscribed from newsletter");

  } catch (error) {
    console.error("Newsletter unsubscribe error:", error);
    return apiError("Internal server error", 500);
  }
}

// Handle GET requests for unsubscribe links in emails
export async function GET(req: NextRequest): Promise<NextResponse<NewsletterResponse>> {
  try {
    const { searchParams } = new URL(req.url);
    const email = searchParams.get('email');

    if (!email) {
      return apiError("Email parameter is required", 400);
    }

    // Find and update subscription
    const subscription = await prisma.newsletterSubscription.findUnique({
      where: { email }
    });

    if (!subscription) {
      return apiError("Email not found in our newsletter list", 404);
    }

    if (!subscription.isActive) {
      return apiSuccess(subscription, "Email is already unsubscribed");
    }

    // Update subscription to inactive
    const updatedSubscription = await prisma.newsletterSubscription.update({
      where: { email },
      data: {
        isActive: false,
        unsubscribedAt: new Date()
      }
    });

    return apiSuccess(updatedSubscription, "Successfully unsubscribed from newsletter");

  } catch (error) {
    console.error("Newsletter unsubscribe error:", error);
    return apiError("Internal server error", 500);
  }
}
