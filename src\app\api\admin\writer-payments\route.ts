import { NextRequest } from "next/server";
import prisma from "@/lib/prisma";
import { checkPermission, apiSuccess, apiError } from "@/lib/api-utils";
import { pricingService } from "@/lib/pricing-service";
import { AssignmentStatus } from "@prisma/client";

export async function GET(req: NextRequest) {
  try {
    // Only admins can access writer payments
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    const { searchParams } = new URL(req.url);
    const status = searchParams.get("status"); // 'pending' or 'approved'
    const writerId = searchParams.get("writerId");
    const limit = parseInt(searchParams.get("limit") || "50");
    const offset = parseInt(searchParams.get("offset") || "0");

    // Base query for assignments that have been completed and assigned to writers
    const baseWhere = {
      status: AssignmentStatus.COMPLETED,
      assignedWriterId: { not: null },
      ...(writerId && { assignedWriterId: writerId }),
    };

    // Fetch assignments and calculate writer compensation
    const assignments = await prisma.assignment.findMany({
      where: baseWhere,
      include: {
        assignedWriter: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
      },
      orderBy: { updatedAt: "desc" },
      take: limit,
      skip: offset,
    });

    // Calculate writer compensation for assignments that don't have it set
    const assignmentsWithCompensation = await Promise.all(
      assignments.map(async (assignment) => {
        let writerCompensation = assignment.writerCompensation;
        
        // Calculate compensation if not already set
        if (!writerCompensation && assignment.price > 0) {
          const compensation = await pricingService.calculateWriterCompensation(
            assignment.price,
            assignment.pageCount
          );
          writerCompensation = compensation.finalAmount;

          // Update the assignment with calculated compensation
          await prisma.assignment.update({
            where: { id: assignment.id },
            data: { 
              writerCompensation,
              writerPaypalEmail: assignment.assignedWriter?.email,
            },
          });
        }

        return {
          id: assignment.id,
          assignmentId: assignment.id,
          writerId: assignment.assignedWriterId!,
          writerCompensation: writerCompensation || 0,
          isWriterPaid: assignment.isWriterPaid,
          writerPaypalEmail: assignment.writerPaypalEmail || assignment.assignedWriter?.email,
          writerPaymentDate: assignment.writerPaymentDate?.toISOString(),
          assignment: {
            id: assignment.id,
            title: assignment.title,
            taskId: assignment.taskId,
            status: assignment.status,
            price: assignment.price,
            createdAt: assignment.createdAt.toISOString(),
            updatedAt: assignment.updatedAt.toISOString(),
          },
          writer: assignment.assignedWriter!,
        };
      })
    );

    // Filter based on payment status
    const pending = assignmentsWithCompensation.filter(p => !p.isWriterPaid);
    const approved = assignmentsWithCompensation.filter(p => p.isWriterPaid);

    // Return based on status filter or all
    if (status === "pending") {
      return apiSuccess({ pending, total: pending.length });
    } else if (status === "approved") {
      return apiSuccess({ approved, total: approved.length });
    } else {
      return apiSuccess({ 
        pending, 
        approved, 
        totals: {
          pending: pending.length,
          approved: approved.length,
          pendingAmount: pending.reduce((sum, p) => sum + p.writerCompensation, 0),
          approvedAmount: approved.reduce((sum, p) => sum + p.writerCompensation, 0),
        }
      });
    }
  } catch (error) {
    console.error("Error fetching writer payments:", error);
    return apiError("Failed to fetch writer payments", 500);
  }
}

// Update writer compensation for all completed assignments (admin utility)
export async function POST(req: NextRequest) {
  try {
    // Only admins can update writer compensations
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    const body = await req.json();
    const { recalculateAll = false } = body;

    if (recalculateAll) {
      // Recalculate compensation for all completed assignments without compensation
      const assignments = await prisma.assignment.findMany({
        where: {
          status: AssignmentStatus.COMPLETED,
          assignedWriterId: { not: null },
          writerCompensation: null,
        },
        include: {
          assignedWriter: {
            select: { email: true },
          },
        },
      });

      let updated = 0;
      for (const assignment of assignments) {
        if (assignment.price > 0) {
          const compensation = await pricingService.calculateWriterCompensation(
            assignment.price,
            assignment.pageCount
          );

          await prisma.assignment.update({
            where: { id: assignment.id },
            data: {
              writerCompensation: compensation.finalAmount,
              writerPaypalEmail: assignment.assignedWriter?.email,
            },
          });
          updated++;
        }
      }

      return apiSuccess({ 
        message: `Updated compensation for ${updated} assignments`,
        updated 
      });
    }

    return apiError("Invalid request", 400);
  } catch (error) {
    console.error("Error updating writer compensations:", error);
    return apiError("Failed to update writer compensations", 500);
  }
}
