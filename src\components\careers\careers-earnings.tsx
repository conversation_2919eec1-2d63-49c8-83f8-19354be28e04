"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  DollarSign, 
  TrendingUp, 
  Award,
  Star,
  Clock,
  Target
} from "lucide-react";

const earningTiers = [
  {
    level: "Beginner Writer",
    rate: "$15-20",
    description: "Perfect for new writers starting their journey",
    requirements: ["Bachelor's degree", "Good English skills", "Basic writing experience"],
    monthlyPotential: "$800-1,200",
    color: "text-blue-600",
    bgColor: "bg-blue-50 dark:bg-blue-950/20",
    progress: 25
  },
  {
    level: "Experienced Writer",
    rate: "$25-35",
    description: "For writers with proven track record and expertise",
    requirements: ["Master's degree preferred", "2+ years experience", "High client ratings"],
    monthlyPotential: "$1,500-2,500",
    color: "text-green-600",
    bgColor: "bg-green-50 dark:bg-green-950/20",
    progress: 60
  },
  {
    level: "Expert Writer",
    rate: "$40-50",
    description: "Top-tier writers with specialized knowledge",
    requirements: ["PhD or equivalent", "5+ years experience", "Subject matter expertise"],
    monthlyPotential: "$3,000-5,000",
    color: "text-purple-600",
    bgColor: "bg-purple-50 dark:bg-purple-950/20",
    progress: 100
  }
];

const bonusOpportunities = [
  {
    icon: Clock,
    title: "Rush Order Bonus",
    description: "Earn 25-50% extra for urgent assignments",
    bonus: "+25-50%"
  },
  {
    icon: Star,
    title: "Quality Bonus",
    description: "Monthly bonus for maintaining high ratings",
    bonus: "Up to $500"
  },
  {
    icon: Target,
    title: "Volume Bonus",
    description: "Complete more orders, earn percentage bonuses",
    bonus: "+10-20%"
  },
  {
    icon: Award,
    title: "Writer of the Month",
    description: "Recognition award with cash prize",
    bonus: "$1,000"
  }
];

export function CareersEarnings() {
  return (
    <div className="container mx-auto px-4">
      <div className="max-w-6xl mx-auto">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <Badge 
            variant="secondary" 
            className="px-4 py-2 text-sm font-medium bg-primary/10 text-primary border-primary/20 mb-4"
          >
            <DollarSign className="w-4 h-4 mr-2" />
            Earning Potential
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            How Much Can You Earn?
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Our transparent pricing structure ensures you know exactly what you can earn at every level of your writing career
          </p>
        </motion.div>

        {/* Earning Tiers */}
        <div className="grid md:grid-cols-3 gap-6 mb-16">
          {earningTiers.map((tier, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
            >
              <Card className="h-full hover:shadow-lg transition-all duration-300 border-2 hover:border-primary/20 group relative overflow-hidden">
                {index === 2 && (
                  <div className="absolute top-0 right-0 bg-primary text-primary-foreground px-3 py-1 text-xs font-semibold rounded-bl-lg">
                    HIGHEST EARNING
                  </div>
                )}
                
                <CardContent className="p-6">
                  {/* Header */}
                  <div className="text-center mb-6">
                    <div className={`inline-flex p-4 rounded-full ${tier.bgColor} mb-4`}>
                      <DollarSign className={`w-8 h-8 ${tier.color}`} />
                    </div>
                    <h3 className="text-xl font-semibold mb-2">{tier.level}</h3>
                    <div className="text-3xl font-bold text-primary mb-1">{tier.rate}</div>
                    <div className="text-sm text-muted-foreground">per page</div>
                  </div>

                  {/* Progress Bar */}
                  <div className="mb-6">
                    <div className="flex justify-between text-sm mb-2">
                      <span>Earning Level</span>
                      <span>{tier.progress}%</span>
                    </div>
                    <Progress value={tier.progress} className="h-2" />
                  </div>

                  {/* Description */}
                  <p className="text-muted-foreground mb-4 text-center">
                    {tier.description}
                  </p>

                  {/* Monthly Potential */}
                  <div className="bg-muted/50 rounded-lg p-4 mb-4 text-center">
                    <div className="text-sm text-muted-foreground mb-1">Monthly Potential</div>
                    <div className="text-lg font-semibold text-primary">{tier.monthlyPotential}</div>
                  </div>

                  {/* Requirements */}
                  <div>
                    <h4 className="font-semibold mb-2 text-sm">Requirements:</h4>
                    <ul className="space-y-1">
                      {tier.requirements.map((req, reqIndex) => (
                        <li key={reqIndex} className="text-sm text-muted-foreground flex items-center">
                          <div className="w-1.5 h-1.5 bg-primary rounded-full mr-2 flex-shrink-0" />
                          {req}
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Bonus Opportunities */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold mb-4">Additional Earning Opportunities</h3>
            <p className="text-muted-foreground">
              Maximize your income with our bonus and incentive programs
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {bonusOpportunities.map((bonus, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="text-center hover:shadow-lg transition-all duration-300 border-2 hover:border-primary/20 group">
                  <CardContent className="p-6">
                    <div className="inline-flex p-3 rounded-lg bg-primary/10 mb-4 group-hover:bg-primary/20 transition-colors">
                      <bonus.icon className="w-6 h-6 text-primary" />
                    </div>
                    <h4 className="font-semibold mb-2 group-hover:text-primary transition-colors">
                      {bonus.title}
                    </h4>
                    <p className="text-sm text-muted-foreground mb-3">
                      {bonus.description}
                    </p>
                    <Badge variant="secondary" className="bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400">
                      {bonus.bonus}
                    </Badge>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Success Story */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mt-16 bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 rounded-2xl p-8 border border-primary/20 text-center"
        >
          <TrendingUp className="w-12 h-12 text-primary mx-auto mb-4" />
          <h3 className="text-2xl font-bold mb-4">Real Writer Success</h3>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            &quot;I started as a beginner writer and now earn over $4,000 monthly working part-time. 
            The flexible schedule allows me to balance my PhD studies while building a sustainable income.&quot;
          </p>
          <div className="text-sm text-muted-foreground">
            - Dr. Sarah M., Expert Writer since 2021
          </div>
        </motion.div>
      </div>
    </div>
  );
}
