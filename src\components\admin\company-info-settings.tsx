// src/components/admin/company-info-settings.tsx
"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import {
  Building2,
  MapPin,
  Phone,
  Save,
  RefreshCw,
  Info,
  AlertCircle
} from "lucide-react";
import { toast } from "sonner";
import { useAdminCompanyInfo } from "@/hooks/use-company-info";

interface CompanyInfoForm {
  companyName: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  phone: string;
  tollFreePhone: string;
  internationalPhone: string;
  supportEmail: string;
  inquiriesEmail: string;
  businessHours: string;
  description: string;
  website: string;
}

export function CompanyInfoSettings() {
  const { companyInfo, loading, error, refetch, updateCompanyInfo, updating } = useAdminCompanyInfo();
  const [form, setForm] = useState<CompanyInfoForm>({
    companyName: "",
    address: "",
    city: "",
    state: "",
    zipCode: "",
    country: "",
    phone: "",
    tollFreePhone: "",
    internationalPhone: "",
    supportEmail: "",
    inquiriesEmail: "",
    businessHours: "",
    description: "",
    website: "",
  });

  const [hasChanges, setHasChanges] = useState(false);

  // Update form when company info is loaded
  useEffect(() => {
    if (companyInfo) {
      setForm({
        companyName: companyInfo.companyName || "",
        address: companyInfo.address || "",
        city: companyInfo.city || "",
        state: companyInfo.state || "",
        zipCode: companyInfo.zipCode || "",
        country: companyInfo.country || "",
        phone: companyInfo.phone || "",
        tollFreePhone: companyInfo.tollFreePhone || "",
        internationalPhone: companyInfo.internationalPhone || "",
        supportEmail: companyInfo.supportEmail || "",
        inquiriesEmail: companyInfo.inquiriesEmail || "",
        businessHours: companyInfo.businessHours || "",
        description: companyInfo.description || "",
        website: companyInfo.website || "",
      });
      setHasChanges(false);
    }
  }, [companyInfo]);

  const handleInputChange = (field: keyof CompanyInfoForm, value: string) => {
    setForm(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const success = await updateCompanyInfo(form);
    
    if (success) {
      toast.success("Company information updated successfully");
      setHasChanges(false);
    } else {
      toast.error("Failed to update company information");
    }
  };

  const handleRefresh = async () => {
    await refetch();
    toast.success("Company information refreshed");
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Company Information
          </CardTitle>
          <CardDescription>
            Manage your company details and contact information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading company information...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Company Information
              {hasChanges && (
                <Badge variant="secondary" className="ml-2">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Unsaved Changes
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Manage your company details and contact information. Changes will be reflected across the entire platform.
            </CardDescription>
          </div>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="sm" onClick={handleRefresh} disabled={updating}>
                <RefreshCw className={`h-4 w-4 ${updating ? 'animate-spin' : ''}`} />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Refresh company information</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </CardHeader>
      <CardContent>
        {error && (
          <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
            <div className="flex items-center gap-2 text-destructive">
              <AlertCircle className="h-4 w-4" />
              <span className="font-medium">Error loading company information</span>
            </div>
            <p className="text-sm text-muted-foreground mt-1">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Company Information */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Building2 className="h-4 w-4 text-primary" />
              <h3 className="text-lg font-semibold">Basic Information</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="companyName">Company Name *</Label>
                <Input
                  id="companyName"
                  value={form.companyName}
                  onChange={(e) => handleInputChange("companyName", e.target.value)}
                  placeholder="Enter company name"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="website">Website</Label>
                <Input
                  id="website"
                  type="url"
                  value={form.website}
                  onChange={(e) => handleInputChange("website", e.target.value)}
                  placeholder="https://example.com"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={form.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
                placeholder="Brief description of your company"
                rows={3}
              />
            </div>
          </div>

          <Separator />

          {/* Address Information */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-primary" />
              <h3 className="text-lg font-semibold">Address Information</h3>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="address">Street Address *</Label>
              <Input
                id="address"
                value={form.address}
                onChange={(e) => handleInputChange("address", e.target.value)}
                placeholder="Enter street address"
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city">City *</Label>
                <Input
                  id="city"
                  value={form.city}
                  onChange={(e) => handleInputChange("city", e.target.value)}
                  placeholder="Enter city"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="state">State/Province *</Label>
                <Input
                  id="state"
                  value={form.state}
                  onChange={(e) => handleInputChange("state", e.target.value)}
                  placeholder="Enter state"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="zipCode">ZIP/Postal Code *</Label>
                <Input
                  id="zipCode"
                  value={form.zipCode}
                  onChange={(e) => handleInputChange("zipCode", e.target.value)}
                  placeholder="Enter ZIP code"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="country">Country *</Label>
              <Input
                id="country"
                value={form.country}
                onChange={(e) => handleInputChange("country", e.target.value)}
                placeholder="Enter country"
                required
              />
            </div>
          </div>

          <Separator />

          {/* Contact Information */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Phone className="h-4 w-4 text-primary" />
              <h3 className="text-lg font-semibold">Contact Information</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phone">Primary Phone *</Label>
                <Input
                  id="phone"
                  value={form.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  placeholder="+****************"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="tollFreePhone">Toll-Free Phone</Label>
                <Input
                  id="tollFreePhone"
                  value={form.tollFreePhone}
                  onChange={(e) => handleInputChange("tollFreePhone", e.target.value)}
                  placeholder="+****************"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="internationalPhone">International Phone</Label>
                <Input
                  id="internationalPhone"
                  value={form.internationalPhone}
                  onChange={(e) => handleInputChange("internationalPhone", e.target.value)}
                  placeholder="+****************"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="supportEmail">Support Email *</Label>
                <Input
                  id="supportEmail"
                  type="email"
                  value={form.supportEmail}
                  onChange={(e) => handleInputChange("supportEmail", e.target.value)}
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="inquiriesEmail">Inquiries Email *</Label>
                <Input
                  id="inquiriesEmail"
                  type="email"
                  value={form.inquiriesEmail}
                  onChange={(e) => handleInputChange("inquiriesEmail", e.target.value)}
                  placeholder="<EMAIL>"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="businessHours">Business Hours *</Label>
              <Input
                id="businessHours"
                value={form.businessHours}
                onChange={(e) => handleInputChange("businessHours", e.target.value)}
                placeholder="Mon-Fri 9am-6pm EST"
                required
              />
            </div>
          </div>

          <Separator />

          {/* Action Buttons */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Info className="h-4 w-4" />
              <span>Changes will be reflected across the entire platform</span>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  if (companyInfo) {
                    setForm({
                      companyName: companyInfo.companyName || "",
                      address: companyInfo.address || "",
                      city: companyInfo.city || "",
                      state: companyInfo.state || "",
                      zipCode: companyInfo.zipCode || "",
                      country: companyInfo.country || "",
                      phone: companyInfo.phone || "",
                      tollFreePhone: companyInfo.tollFreePhone || "",
                      internationalPhone: companyInfo.internationalPhone || "",
                      supportEmail: companyInfo.supportEmail || "",
                      inquiriesEmail: companyInfo.inquiriesEmail || "",
                      businessHours: companyInfo.businessHours || "",
                      description: companyInfo.description || "",
                      website: companyInfo.website || "",
                    });
                    setHasChanges(false);
                  }
                }}
                disabled={!hasChanges || updating}
              >
                Reset
              </Button>
              
              <Button type="submit" disabled={!hasChanges || updating}>
                {updating ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Changes
                  </>
                )}
              </Button>
            </div>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
