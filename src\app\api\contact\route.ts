import { NextRequest, NextResponse } from "next/server";
import { Resend } from "resend";
import { contactFormSchema } from "@/lib/validations";
import { parseRequestBody, apiSuccess, apiError } from "@/lib/api-utils";
import type { ContactFormRequest } from "@/types/api";

const resend = new Resend(process.env.RESEND_API_KEY);
const fromAddress = process.env.RESEND_VERIFIED_DOMAIN
  ? `noreply@${process.env.RESEND_VERIFIED_DOMAIN}`
  : "<EMAIL>";
const supportEmail = `support@${process.env.RESEND_VERIFIED_DOMAIN || "homeworkassylum.com"}`;

function getContactEmailTemplate(data: ContactFormRequest): string {
  const urgencyColors = {
    low: "#10b981",
    medium: "#f59e0b", 
    high: "#ef4444"
  };

  const categoryLabels = {
    general: "General Inquiry",
    support: "Technical Support",
    billing: "Billing & Payment",
    technical: "Technical Issue",
    partnership: "Partnership Opportunity"
  };

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>New Contact Form Submission</title>
      <style>
        body {
          margin: 0;
          padding: 0;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
          line-height: 1.6;
          color: #333333;
          background-color: #f8f9fa;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          background-color: #ffffff;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          padding: 30px;
          text-align: center;
        }
        .header h1 {
          color: #ffffff;
          margin: 0;
          font-size: 24px;
          font-weight: 600;
        }
        .content {
          padding: 30px;
        }
        .info-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 20px;
          margin-bottom: 30px;
        }
        .info-item {
          background-color: #f8f9fa;
          padding: 15px;
          border-radius: 6px;
          border-left: 4px solid #667eea;
        }
        .info-label {
          font-weight: 600;
          color: #4a5568;
          font-size: 12px;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          margin-bottom: 5px;
        }
        .info-value {
          color: #2d3748;
          font-size: 14px;
        }
        .urgency-badge {
          display: inline-block;
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 600;
          text-transform: uppercase;
          color: white;
        }
        .message-section {
          background-color: #f7fafc;
          padding: 20px;
          border-radius: 6px;
          margin: 20px 0;
        }
        .message-section h3 {
          color: #2d3748;
          margin-top: 0;
          margin-bottom: 15px;
        }
        .message-content {
          color: #4a5568;
          white-space: pre-wrap;
          line-height: 1.7;
        }
        .footer {
          background-color: #f7fafc;
          padding: 20px;
          text-align: center;
          border-top: 1px solid #e2e8f0;
        }
        .footer p {
          margin: 0;
          font-size: 12px;
          color: #718096;
        }
        @media (max-width: 600px) {
          .info-grid {
            grid-template-columns: 1fr;
          }
          .container {
            margin: 0 10px;
          }
          .header, .content, .footer {
            padding: 20px;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>📧 New Contact Form Submission</h1>
        </div>
        
        <div class="content">
          <div class="info-grid">
            <div class="info-item">
              <div class="info-label">Name</div>
              <div class="info-value">${data.name}</div>
            </div>
            <div class="info-item">
              <div class="info-label">Email</div>
              <div class="info-value">${data.email}</div>
            </div>
            <div class="info-item">
              <div class="info-label">Phone</div>
              <div class="info-value">${data.phone || "Not provided"}</div>
            </div>
            <div class="info-item">
              <div class="info-label">Category</div>
              <div class="info-value">${categoryLabels[data.category]}</div>
            </div>
          </div>

          <div class="info-item" style="margin-bottom: 20px;">
            <div class="info-label">Subject</div>
            <div class="info-value">${data.subject}</div>
          </div>

          <div class="info-item" style="margin-bottom: 20px;">
            <div class="info-label">Urgency Level</div>
            <div class="info-value">
              <span class="urgency-badge" style="background-color: ${urgencyColors[data.urgency]};">
                ${data.urgency.toUpperCase()}
              </span>
            </div>
          </div>
          
          <div class="message-section">
            <h3>Message:</h3>
            <div class="message-content">${data.message}</div>
          </div>
        </div>
        
        <div class="footer">
          <p>
            This message was sent via the contact form on ${process.env.RESEND_VERIFIED_DOMAIN}<br>
            Submitted at: ${new Date().toLocaleString()}
          </p>
        </div>
      </div>
    </body>
    </html>
  `;
}

function getAutoReplyTemplate(data: ContactFormRequest): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Thank you for contacting us</title>
      <style>
        body {
          margin: 0;
          padding: 0;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
          line-height: 1.6;
          color: #333333;
          background-color: #f8f9fa;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          background-color: #ffffff;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
          background: linear-gradient(135deg, #10b981 0%, #059669 100%);
          padding: 40px 30px;
          text-align: center;
        }
        .header h1 {
          color: #ffffff;
          margin: 0;
          font-size: 28px;
          font-weight: 600;
        }
        .content {
          padding: 40px 30px;
        }
        .greeting {
          font-size: 18px;
          color: #2d3748;
          margin-bottom: 20px;
        }
        .message {
          font-size: 16px;
          color: #4a5568;
          margin-bottom: 30px;
          line-height: 1.7;
        }
        .info-box {
          background-color: #f0f9ff;
          border-left: 4px solid #0ea5e9;
          padding: 20px;
          margin: 20px 0;
          border-radius: 0 6px 6px 0;
        }
        .response-time {
          background-color: #f0fdf4;
          border: 1px solid #bbf7d0;
          padding: 15px;
          border-radius: 6px;
          margin: 20px 0;
          text-align: center;
        }
        .footer {
          background-color: #f7fafc;
          padding: 30px;
          text-align: center;
          border-top: 1px solid #e2e8f0;
        }
        .footer p {
          margin: 0;
          font-size: 14px;
          color: #718096;
        }
        @media (max-width: 600px) {
          .container {
            margin: 0 10px;
          }
          .header, .content, .footer {
            padding: 20px;
          }
          .header h1 {
            font-size: 24px;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>✅ Message Received!</h1>
        </div>
        
        <div class="content">
          <p class="greeting">Hi ${data.name},</p>
          
          <p class="message">
            Thank you for reaching out to us! We have successfully received your message and our team will review it shortly.
          </p>
          
          <div class="info-box">
            <strong>Your inquiry details:</strong><br>
            <strong>Subject:</strong> ${data.subject}<br>
            <strong>Category:</strong> ${data.category}<br>
            <strong>Priority:</strong> ${data.urgency.toUpperCase()}
          </div>
          
          <div class="response-time">
            <strong>⏱️ Expected Response Time</strong><br>
            ${data.urgency === 'high' ? 'Within 2-4 hours' : 
              data.urgency === 'medium' ? 'Within 24 hours' : 
              'Within 48 hours'}
          </div>
          
          <p class="message">
            In the meantime, you can explore our <a href="${process.env.NEXTAUTH_URL}/faqs" style="color: #0ea5e9;">FAQ section</a> 
            for quick answers to common questions, or visit our <a href="${process.env.NEXTAUTH_URL}/services" style="color: #0ea5e9;">services page</a> 
            to learn more about what we offer.
          </p>
          
          <p class="message">
            If you have any urgent concerns, please don$apos;t hesitate to call our support line.
          </p>
        </div>
        
        <div class="footer">
          <p>
            Best regards,<br>
            The Support Team<br>
            ${process.env.RESEND_VERIFIED_DOMAIN}
          </p>
        </div>
      </div>
    </body>
    </html>
  `;
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Parse and validate request body
    const body = await parseRequestBody(req, contactFormSchema);
    if ('success' in body && !body.success) {
      return apiError(body.message, 400, body.errors);
    }

    const contactData = body as ContactFormRequest;

    // Send email to support team
    try {
      await resend.emails.send({
        from: fromAddress,
        to: supportEmail,
        subject: `[${contactData.urgency.toUpperCase()}] ${contactData.subject}`,
        html: getContactEmailTemplate(contactData),
      });
    } catch (emailError) {
      console.error("Failed to send contact email to support:", emailError);
      return apiError("Failed to send your message. Please try again later.", 500);
    }

    // Send auto-reply to user
    try {
      await resend.emails.send({
        from: fromAddress,
        to: contactData.email,
        subject: "Thank you for contacting us - We$apos;ve received your message",
        html: getAutoReplyTemplate(contactData),
      });
    } catch (emailError) {
      console.error("Failed to send auto-reply email:", emailError);
      // Don't fail the request if auto-reply fails
    }

    return apiSuccess(
      {
        id: `contact_${Date.now()}`,
        submittedAt: new Date().toISOString(),
      },
      "Your message has been sent successfully! We$apos;ll get back to you soon."
    );

  } catch (error) {
    console.error("Contact form submission error:", error);
    return apiError("Internal server error", 500);
  }
}
