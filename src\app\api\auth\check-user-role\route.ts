import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

export async function POST(req: NextRequest) {
  try {
    const { email } = await req.json();

    if (!email) {
      return NextResponse.json(
        { error: "Email is required" },
        { status: 400 }
      );
    }

    // Find the user by email
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        role: true,
        email: true,
      },
    });

    if (!user) {
      // User doesn't exist - return false so normal login flow continues
      return NextResponse.json({
        exists: false,
        role: null,
      });
    }

    // User exists - return their role
    return NextResponse.json({
      exists: true,
      role: user.role,
    });
  } catch (error) {
    console.error("Error checking user role:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
