"use client";

import { motion } from "framer-motion";
import { Check, FileText, CreditCard, User<PERSON>he<PERSON>, CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";

interface ProgressStepsProps {
  currentStep: number;
  className?: string;
}

const steps = [
  {
    id: 1,
    title: "Order Details",
    description: "Fill out assignment requirements",
    icon: FileText,
  },
  {
    id: 2,
    title: "Review & Pricing",
    description: "Confirm details and pricing",
    icon: CreditCard,
  },
  {
    id: 3,
    title: "Account Setup",
    description: "Create or login to account",
    icon: UserCheck,
  },
  {
    id: 4,
    title: "Order Complete",
    description: "Assignment submitted successfully",
    icon: CheckCircle,
  },
];

export function ProgressSteps({ currentStep, className = "" }: ProgressStepsProps) {
  return (
    <div className={cn("w-full max-w-4xl mx-auto", className)}>
      <div className="flex items-center justify-between relative">
        {/* Progress Line */}
        <div className="absolute top-5 left-0 w-full h-0.5 bg-muted -z-10">
          <motion.div
            className="h-full bg-primary"
            initial={{ width: "0%" }}
            animate={{ 
              width: `${((currentStep - 1) / (steps.length - 1)) * 100}%` 
            }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
          />
        </div>

        {steps.map((step, index) => {
          const isCompleted = currentStep > step.id;
          const isCurrent = currentStep === step.id;
          const isUpcoming = currentStep < step.id;
          const IconComponent = step.icon;

          return (
            <div
              key={step.id}
              className="flex flex-col items-center relative z-10"
            >
              {/* Step Circle */}
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className={cn(
                  "w-10 h-10 rounded-full flex items-center justify-center border-2 transition-all duration-300",
                  {
                    "bg-primary border-primary text-primary-foreground": isCompleted,
                    "bg-primary border-primary text-primary-foreground ring-4 ring-primary/20": isCurrent,
                    "bg-background border-muted-foreground/30 text-muted-foreground": isUpcoming,
                  }
                )}
              >
                {isCompleted ? (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Check className="w-5 h-5" />
                  </motion.div>
                ) : (
                  <IconComponent className="w-5 h-5" />
                )}
              </motion.div>

              {/* Step Content */}
              <div className="mt-3 text-center max-w-[120px]">
                <motion.h3
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 + 0.1 }}
                  className={cn(
                    "text-sm font-medium transition-colors duration-300",
                    {
                      "text-primary": isCompleted || isCurrent,
                      "text-muted-foreground": isUpcoming,
                    }
                  )}
                >
                  {step.title}
                </motion.h3>
                <motion.p
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 + 0.2 }}
                  className="text-xs text-muted-foreground mt-1 hidden sm:block"
                >
                  {step.description}
                </motion.p>
              </div>

              {/* Step Number for Mobile */}
              <div className="sm:hidden absolute -bottom-8 left-1/2 transform -translate-x-1/2">
                <span className="text-xs text-muted-foreground">
                  {step.id}/{steps.length}
                </span>
              </div>
            </div>
          );
        })}
      </div>

      {/* Mobile Step Description */}
      <div className="sm:hidden mt-12 text-center">
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <h3 className="text-sm font-medium text-foreground">
            {steps[currentStep - 1]?.title}
          </h3>
          <p className="text-xs text-muted-foreground mt-1">
            {steps[currentStep - 1]?.description}
          </p>
        </motion.div>
      </div>

      {/* Progress Percentage */}
      <div className="mt-6 text-center">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="text-sm text-muted-foreground"
        >
          Step {currentStep} of {steps.length} •{" "}
          <span className="text-primary font-medium">
            {Math.round((currentStep / steps.length) * 100)}% Complete
          </span>
        </motion.div>
      </div>
    </div>
  );
}

// Simplified version for smaller spaces
export function MiniProgressSteps({ currentStep, className = "" }: ProgressStepsProps) {
  return (
    <div className={cn("flex items-center justify-center space-x-2", className)}>
      {steps.map((step, index) => {
        const isCompleted = currentStep > step.id;
        const isCurrent = currentStep === step.id;
        const isUpcoming = currentStep < step.id;

        return (
          <div key={step.id} className="flex items-center">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
              className={cn(
                "w-3 h-3 rounded-full transition-all duration-300",
                {
                  "bg-primary": isCompleted || isCurrent,
                  "bg-muted": isUpcoming,
                }
              )}
            />
            {index < steps.length - 1 && (
              <div
                className={cn(
                  "w-8 h-0.5 mx-1 transition-colors duration-300",
                  {
                    "bg-primary": isCompleted,
                    "bg-muted": !isCompleted,
                  }
                )}
              />
            )}
          </div>
        );
      })}
      <span className="ml-3 text-xs text-muted-foreground">
        {currentStep}/{steps.length}
      </span>
    </div>
  );
}
