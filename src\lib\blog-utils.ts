/**
 * Blog utility functions for HTML parsing and content manipulation
 */

/**
 * Parses HTML content and finds all H2 headings with their positions
 */
export function findH2Headings(htmlContent: string): Array<{ element: Element; index: number; text: string }> {
  if (typeof window === 'undefined') {
    // Server-side: use a more flexible regex approach
    const h2Regex = /<h2[^>]*>([\s\S]*?)<\/h2>/gi;
    const headings: Array<{ element: Element; index: number; text: string }> = [];
    let match;

    while ((match = h2Regex.exec(htmlContent)) !== null) {
      headings.push({
        element: null as unknown as Element, // Placeholder for server-side
        index: match.index,
        text: match[1].replace(/<[^>]*>/g, '').trim() // Strip HTML tags from text and trim
      });
    }

    return headings;
  }

  // Client-side: use DOM parser
  const parser = new DOMParser();
  const doc = parser.parseFromString(htmlContent, 'text/html');
  const h2Elements = doc.querySelectorAll('h2');

  console.log('Client-side H2 headings found:', h2Elements.length);
  return Array.from(h2Elements).map((element, index) => ({
    element,
    index,
    text: element.textContent || ''
  }));
}

/**
 * Inserts Call-to-Action component HTML before specified H2 headings
 * Places CTA before 3rd H2, and if more than 10 H2s exist, also before 8th H2
 */
export function insertCallToActionInContent(htmlContent: string): string {
  if (typeof window === 'undefined') {
    // Server-side processing
    return insertCallToActionServerSide(htmlContent);
  }

  // Client-side processing
  return insertCallToActionClientSide(htmlContent);
}

/**
 * Server-side CTA insertion using regex
 */
function insertCallToActionServerSide(htmlContent: string): string {
  // More flexible regex to match H2 tags with any attributes and content
  const h2Regex = /<h2[^>]*>[\s\S]*?<\/h2>/gi;
  const matches = Array.from(htmlContent.matchAll(h2Regex));

  if (matches.length < 3) {
    return htmlContent; // Not enough H2s to insert CTA
  }

  const ctaHtml = generateCallToActionHTML();
  let modifiedContent = htmlContent;
  let insertionOffset = 0;

  // Insert before 3rd H2 (index 2)
  if (matches[2]) {
    const insertPosition = matches[2].index! + insertionOffset;
    const ctaWithId = ctaHtml.replace('data-cta="blog-insert"', 'data-cta="blog-insert" data-cta-id="cta-1"');
    modifiedContent =
      modifiedContent.slice(0, insertPosition) +
      ctaWithId +
      modifiedContent.slice(insertPosition);
    insertionOffset += ctaWithId.length;
  }

  // Insert before 8th H2 (index 7) if more than 10 H2s exist
  if (matches.length > 10 && matches[7]) {
    const insertPosition = matches[7].index! + insertionOffset;
    const ctaWithId = ctaHtml.replace('data-cta="blog-insert"', 'data-cta="blog-insert" data-cta-id="cta-2"');
    modifiedContent =
      modifiedContent.slice(0, insertPosition) +
      ctaWithId +
      modifiedContent.slice(insertPosition);
  }

  return modifiedContent;
}

/**
 * Client-side CTA insertion using DOM manipulation
 */
function insertCallToActionClientSide(htmlContent: string): string {
  const parser = new DOMParser();
  const doc = parser.parseFromString(htmlContent, 'text/html');
  const h2Elements = doc.querySelectorAll('h2');

  if (h2Elements.length < 3) {
    return htmlContent; // Not enough H2s to insert CTA
  }

  const ctaHtml = generateCallToActionHTML();
  const ctaDoc = parser.parseFromString(ctaHtml, 'text/html');
  const ctaElement = ctaDoc.body.firstElementChild;

  if (!ctaElement) {
    console.error('Failed to parse CTA HTML');
    return htmlContent;
  }

  // Insert before 3rd H2 (index 2)
  if (h2Elements[2]) {
    const clonedCTA = ctaElement.cloneNode(true) as Element;
    clonedCTA.setAttribute('data-cta-id', 'cta-1');
    h2Elements[2].parentNode?.insertBefore(clonedCTA, h2Elements[2]);
  }

  // Insert before 8th H2 (index 7) if more than 10 H2s exist
  if (h2Elements.length > 10 && h2Elements[7]) {
    const clonedCTA = ctaElement.cloneNode(true) as Element;
    clonedCTA.setAttribute('data-cta-id', 'cta-2');
    h2Elements[7].parentNode?.insertBefore(clonedCTA, h2Elements[7]);
  }

  return doc.body.innerHTML;
}

/**
 * Generates the HTML for the Call-to-Action component
 * This is a static HTML version of the React component for insertion into blog content
 */
function generateCallToActionHTML(): string {
  return `
    <div class="call-to-action-wrapper my-6 lg:my-8" data-cta="blog-insert">
      <div class="relative overflow-hidden bg-gradient-to-br from-orange-50 via-teal-50 to-orange-50 dark:from-orange-950/20 dark:via-teal-950/20 dark:to-orange-950/20 border-2 border-orange-200/50 dark:border-orange-800/30 shadow-lg rounded-lg">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-5">
          <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23000000\" fill-opacity=\"0.1\"%3E%3Ccircle cx=\"7\" cy=\"7\" r=\"1\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
        </div>

        <div class="relative p-4 lg:p-6">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6 items-center">
            <!-- Left Content -->
            <div class="space-y-4">
              <div class="space-y-3">
                <h3 class="text-xl lg:text-2xl font-bold leading-tight">
                  <span class="bg-gradient-to-r from-orange-600 to-orange-500 bg-clip-text text-transparent">
                    Enhance your
                  </span>
                  <span class="bg-gradient-to-r from-teal-600 to-teal-500 bg-clip-text text-transparent">
                    Academic Performance
                  </span>
                  <span class="bg-gradient-to-r from-orange-600 to-orange-500 bg-clip-text text-transparent">
                    And Reach Your Goals
                  </span>
                </h3>

                <p class="text-sm lg:text-base text-muted-foreground leading-relaxed">
                  See how quick and easy it is to get an exceptional essay with minimal effort on our platform
                </p>
              </div>

              <div>
                <a href="/create-order" class="inline-flex items-center justify-center px-4 py-2 lg:px-6 lg:py-3 text-sm lg:text-base text-white font-semibold bg-gradient-to-r from-teal-600 to-teal-500 hover:from-teal-700 hover:to-teal-600 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105 text-decoration-none">
                  <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                  </svg>
                  GET HOMEWORK HELP
                </a>
              </div>
            </div>

            <!-- Right Illustration -->
            <div class="relative flex justify-center lg:justify-end">
              <div class="relative">
                <!-- Main Card -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-4 w-64 lg:w-72 border border-orange-200/30 dark:border-orange-800/30">
                  <!-- Header -->
                  <div class="flex items-center justify-between mb-4">
                    <div class="w-2 h-2 bg-red-400 rounded-full"></div>
                    <div class="w-2 h-2 bg-yellow-400 rounded-full"></div>
                    <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                  </div>

                  <!-- Content Lines with Checkmarks -->
                  <div class="space-y-3">
                    <div class="flex items-center space-x-2">
                      <svg class="h-3 w-3 text-teal-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      <div class="flex-1">
                        <div class="h-1.5 bg-gradient-to-r from-gray-200 to-gray-100 dark:from-gray-600 dark:to-gray-700 rounded-full w-full"></div>
                      </div>
                    </div>
                    <div class="flex items-center space-x-2">
                      <svg class="h-3 w-3 text-teal-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      <div class="flex-1">
                        <div class="h-1.5 bg-gradient-to-r from-gray-200 to-gray-100 dark:from-gray-600 dark:to-gray-700 rounded-full w-4/5"></div>
                      </div>
                    </div>
                    <div class="flex items-center space-x-2">
                      <svg class="h-3 w-3 text-teal-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      <div class="flex-1">
                        <div class="h-1.5 bg-gradient-to-r from-gray-200 to-gray-100 dark:from-gray-600 dark:to-gray-700 rounded-full w-3/4"></div>
                      </div>
                    </div>
                  </div>

                  <!-- Bottom Section -->
                  <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <div class="flex items-center space-x-3">
                      <svg class="h-6 w-6 text-teal-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      <div class="h-3 bg-gradient-to-r from-teal-400 to-teal-300 rounded-full w-20"></div>
                    </div>
                  </div>
                </div>

                <!-- Character Illustration -->
                <div class="absolute -right-8 -bottom-4 z-10">
                  <div class="w-24 h-32 bg-gradient-to-b from-orange-400 to-orange-500 rounded-full relative">
                    <!-- Simple character representation -->
                    <div class="absolute top-2 left-1/2 transform -translate-x-1/2 w-16 h-16 bg-orange-300 rounded-full"></div>
                    <div class="absolute top-6 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-white rounded-full"></div>
                    <div class="absolute top-6 right-4 w-2 h-2 bg-white rounded-full"></div>
                    <div class="absolute top-10 left-1/2 transform -translate-x-1/2 w-4 h-1 bg-white rounded-full"></div>
                    
                    <!-- Pencil -->
                    <div class="absolute -top-2 -right-2 w-8 h-2 bg-yellow-400 rounded-full transform rotate-45">
                      <div class="absolute right-0 top-0 w-2 h-2 bg-gray-600 rounded-full"></div>
                    </div>
                  </div>
                </div>

                <!-- Floating Elements -->
                <div class="absolute -top-4 -left-4 w-8 h-8 bg-orange-200 dark:bg-orange-800 rounded-full flex items-center justify-center">
                  <svg class="h-4 w-4 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>

                <div class="absolute top-8 -right-12 w-6 h-6 bg-teal-200 dark:bg-teal-800 rounded-full flex items-center justify-center">
                  <svg class="h-3 w-3 text-teal-600 dark:text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

/**
 * Checks if content already has CTA inserted to avoid duplicates
 */
export function hasCallToActionInserted(htmlContent: string): boolean {
  return htmlContent.includes('data-cta="blog-insert"');
}

/**
 * Removes existing CTAs from content (useful for re-processing)
 */
export function removeCallToActionFromContent(htmlContent: string): string {
  // Use a more compatible regex without the 's' flag
  const ctaRegex = /<div class="call-to-action-wrapper[^>]*data-cta="blog-insert"[^>]*>[\s\S]*?<\/div>\s*<\/div>/gi;
  return htmlContent.replace(ctaRegex, '');
}
