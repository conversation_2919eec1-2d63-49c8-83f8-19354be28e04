import { NextRequest } from 'next/server';
import { supabase } from '@/lib/supabase';
import { nanoid } from 'nanoid';
import prisma from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { validateFileType, validateFileSize, apiError, apiSuccess, sanitizeFileName } from '@/lib/api-utils';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authConfig);
    if (!session?.user) {
      return apiError('Unauthorized', 401);
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const assignmentId = formData.get('assignmentId') as string;
    const folder = formData.get('folder') as string || 'assignments';

    if (!file) {
      return apiError('No file provided', 400);
    }

    if (!assignmentId) {
      return apiError('Assignment ID is required', 400);
    }

    // Validate file type using utility function
    if (!validateFileType(file.type)) {
      return apiError('Invalid file type. Only documents (PDF, DOC, DOCX, PPT, PPTX, TXT, RTF, XLS, XLSX) are allowed.', 400);
    }

    // Validate file size using utility function
    if (!validateFileSize(file.size, 50)) {
      return apiError('File size too large. Maximum size is 50MB.', 400);
    }

    // Verify assignment exists and user has access
    const assignment = await prisma.assignment.findUnique({
      where: { id: assignmentId },
      include: { client: true, assignedWriter: true }
    });

    if (!assignment) {
      return apiError('Assignment not found', 404);
    }

    // Check if user has permission to upload files for this assignment
    const isAuthorized =
      session.user.role === 'ADMIN' ||
      assignment.clientId === session.user.id ||
      assignment.assignedWriterId === session.user.id;

    if (!isAuthorized) {
      return apiError('Unauthorized to upload files for this assignment', 403);
    }

    // Generate unique file path with sanitized filename
    const fileExtension = file.name.split('.').pop();
    const originalFileName = file.name.replace(`.${fileExtension}`, '');
    const sanitizedFileName = sanitizeFileName(originalFileName);
    const uniqueId = nanoid(8);

    // Create folder structure with assignment ID
    const assignmentFolder = `${folder}/${assignmentId}`;
    const filePath = `${assignmentFolder}/${sanitizedFileName}_${uniqueId}.${fileExtension}`;

    // Note: Folder will be created automatically when we upload the first file
    // Supabase Storage creates folders automatically during file upload

    // Upload file to Supabase Storage
    const { data, error: uploadError } = await supabase.storage
      .from('academic-files')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) {
      console.error('Supabase upload error:', uploadError);
      return apiError(uploadError.message || 'Upload failed', 500);
    }

    if (!data) {
      return apiError('Upload failed - no data returned', 500);
    }

    // Get public URL for the uploaded file
    const { data: urlData } = supabase.storage
      .from('academic-files')
      .getPublicUrl(data.path);

    if (!urlData.publicUrl) {
      return apiError('Failed to get public URL', 500);
    }

    // Save file attachment record to database
    const fileAttachment = await prisma.fileAttachment.create({
      data: {
        assignmentId,
        fileName: `${sanitizedFileName}_${uniqueId}.${fileExtension}`,
        originalName: file.name,
        fileUrl: urlData.publicUrl,
        fileSize: file.size,
        fileType: file.type
      }
    });

    return apiSuccess({
      id: fileAttachment.id,
      url: fileAttachment.fileUrl,
      path: data.path,
      name: fileAttachment.originalName,
      size: fileAttachment.fileSize,
      type: fileAttachment.fileType,
      uploadedAt: fileAttachment.uploadedAt.toISOString()
    }, 'File uploaded successfully');

  } catch (error) {
    console.error('File upload error:', error);
    return apiError('Internal server error', 500);
  }
}
