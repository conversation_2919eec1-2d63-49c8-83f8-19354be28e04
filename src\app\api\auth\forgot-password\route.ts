import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { Resend } from "resend";
import { forgotPasswordSchema } from "@/lib/validations";
import { parseRequestBody, apiError, apiSuccess, generateResetCode } from "@/lib/api-utils";

const resend = new Resend(process.env.RESEND_API_KEY);
const fromAddress = process.env.RESEND_VERIFIED_DOMAIN
  ? `noreply@${process.env.RESEND_VERIFIED_DOMAIN}`
  : "<EMAIL>";

function getPasswordResetEmailTemplate(resetCode: string, userEmail: string): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Reset Your Password</title>
      <style>
        body {
          margin: 0;
          padding: 0;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
          line-height: 1.6;
          color: #333333;
          background-color: #f8f9fa;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          background-color: #ffffff;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          padding: 40px 30px;
          text-align: center;
        }
        .header h1 {
          color: #ffffff;
          margin: 0;
          font-size: 28px;
          font-weight: 600;
        }
        .content {
          padding: 40px 30px;
        }
        .reset-code {
          background-color: #f8f9fa;
          border: 2px dashed #667eea;
          border-radius: 8px;
          padding: 20px;
          text-align: center;
          margin: 30px 0;
        }
        .code-display {
          font-size: 32px;
          font-weight: bold;
          color: #667eea;
          letter-spacing: 8px;
          font-family: 'Courier New', monospace;
          margin: 10px 0;
        }
        .security-note {
          background-color: #fff3cd;
          border-left: 4px solid #ffc107;
          padding: 15px;
          margin: 20px 0;
          border-radius: 4px;
        }
        .footer {
          background-color: #f8f9fa;
          padding: 20px 30px;
          text-align: center;
          color: #6c757d;
          font-size: 14px;
        }
        .btn {
          display: inline-block;
          padding: 12px 24px;
          background-color: #667eea;
          color: #ffffff;
          text-decoration: none;
          border-radius: 6px;
          font-weight: 600;
          margin: 20px 0;
        }
        .instructions {
          background-color: #e7f3ff;
          border-left: 4px solid #2196f3;
          padding: 15px;
          margin: 20px 0;
          border-radius: 4px;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🔐 Password Reset Request</h1>
        </div>

        <div class="content">
          <p style="font-size: 18px; margin-bottom: 20px;">
            Hello,
          </p>

          <p>
            We received a request to reset the password for your Academic App account 
            associated with <strong>${userEmail}</strong>.
          </p>

          <div class="instructions">
            <p><strong>📱 How to reset your password:</strong></p>
            <ol>
              <li>Go to the password reset page on Academic App</li>
              <li>Enter the 6-digit verification code below</li>
              <li>Create your new password</li>
            </ol>
          </div>

          <div class="reset-code">
            <p style="margin: 0; font-size: 16px; color: #666;">Your verification code is:</p>
            <div class="code-display">${resetCode}</div>
            <p style="margin: 0; font-size: 14px; color: #666;">
              This code will expire in 15 minutes
            </p>
          </div>

          <div class="security-note">
            <p>
              <strong>🔒 Security Notice:</strong> This verification code will expire in 15 minutes.
              If you didn't request a password reset, please ignore this email and your password will remain unchanged.
            </p>
          </div>

          <p>
            For security reasons, please do not share this code with anyone. Our support team will never ask for your verification code.
          </p>
        </div>

        <div class="footer">
          <p>
            This email was sent from Academic App.<br>
            If you have any questions, please contact our support team.
          </p>
          <p style="margin-top: 15px; font-size: 12px;">
            © 2024 Academic App. All rights reserved.
          </p>
        </div>
      </div>
    </body>
    </html>
  `;
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Parse and validate request body
    const body = await parseRequestBody(req, forgotPasswordSchema);
    if ('success' in body && !body.success) {
      return apiError(body.message, 400, body.errors);
    }

    const requestData = body as { email: string };
    const { email } = requestData;

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { email },
      select: { id: true, email: true, name: true }
    });

    // Always return success to prevent email enumeration attacks
    // But only send email if user actually exists
    if (user) {
      // Remove any existing password reset tokens for this user
      await prisma.passwordResetToken.deleteMany({
        where: { userId: user.id }
      });

      // Generate new 6-digit code
      const resetCode = generateResetCode();
      const expires = new Date(Date.now() + 1000 * 60 * 15); // 15 minutes

      // Create new password reset token
      await prisma.passwordResetToken.create({
        data: {
          userId: user.id,
          code: resetCode,
          expires,
          isUsed: false
        }
      });

      // Send email with reset code
      try {
        const result = await resend.emails.send({
          from: fromAddress,
          to: email,
          subject: "Reset your password - Academic App",
          html: getPasswordResetEmailTemplate(resetCode, email),
        });

        if (result.error) {
          console.error("Resend error:", result.error);
          return apiError("Failed to send reset email. Please try again later.", 500);
        }
      } catch (emailError) {
        console.error("Email send exception:", emailError);
        return apiError("Failed to send reset email. Please try again later.", 500);
      }
    }

    // Always return success message
    return apiSuccess(
      { message: "If an account with that email exists, we've sent a password reset code." },
      "Password reset email sent"
    );

  } catch (error) {
    console.error("Forgot password error:", error);
    return apiError("An error occurred while processing your request", 500);
  }
}
