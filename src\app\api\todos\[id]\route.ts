// src/app/api/todos/[id]/route.ts
import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import {
  apiSuccess,
  apiError,
  parseRequestBody,
  getCurrentUserId,
  safeJsonDate,
} from "@/lib/api-utils";
import { todoUpdateSchema } from "@/lib/validations";
import { TodoResponse, TodoUpdateData } from "@/types/api";
import { Prisma } from "@prisma/client";

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const currentUserId = await getCurrentUserId();
    if (!currentUserId) {
      return apiError("Authentication required", 401);
    }

    const { id } = await params;

    const todo = await prisma.todo.findFirst({
      where: {
        id,
        userId: currentUserId, // Ensure user can only access their own todos
      },
    });

    if (!todo) {
      return apiError("Todo not found", 404);
    }

    return apiSuccess(safeJsonDate(todo) as TodoResponse);
  } catch (error) {
    console.error("Error fetching todo:", error);
    return apiError("Failed to fetch todo", 500);
  }
}

export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const currentUserId = await getCurrentUserId();
    if (!currentUserId) {
      return apiError("Authentication required", 401);
    }

    const { id } = await params;

    // Check if todo exists and belongs to current user
    const existingTodo = await prisma.todo.findFirst({
      where: {
        id,
        userId: currentUserId,
      },
    });

    if (!existingTodo) {
      return apiError("Todo not found", 404);
    }

    // Parse and validate request body
    const result = await parseRequestBody(req, todoUpdateSchema);
    if ("success" in result && result.success === false) {
      return apiError(result.message, 400, result.errors);
    }

    const updateData = result as TodoUpdateData;

    // Prepare update data
    const updatePayload: Prisma.TodoUpdateInput = { ...updateData };
    if (updateData.dueDate) {
      updatePayload.dueDate = new Date(updateData.dueDate);
    }

    const updatedTodo = await prisma.todo.update({
      where: { id },
      data: updatePayload,
    });

    return apiSuccess(
      safeJsonDate(updatedTodo) as TodoResponse,
      "Todo updated successfully"
    );
  } catch (error) {
    console.error("Error updating todo:", error);
    return apiError("Failed to update todo", 500);
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const currentUserId = await getCurrentUserId();
    if (!currentUserId) {
      return apiError("Authentication required", 401);
    }

    const { id } = await params;

    // Check if todo exists and belongs to current user
    const existingTodo = await prisma.todo.findFirst({
      where: {
        id,
        userId: currentUserId,
      },
    });

    if (!existingTodo) {
      return apiError("Todo not found", 404);
    }

    await prisma.todo.delete({
      where: { id },
    });

    return apiSuccess(null, "Todo deleted successfully");
  } catch (error) {
    console.error("Error deleting todo:", error);
    return apiError("Failed to delete todo", 500);
  }
}
