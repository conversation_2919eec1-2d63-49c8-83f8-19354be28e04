import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
  try {
    

    // Get all blog posts that don't have alt text
    const blogs = await prisma.blog.findMany({
      select: {
        id: true,
        title: true,
        imageUrl: true,
        slug: true,
      },
    });

    

    // Define alt text based on the blog content/title
    const altTextMap: Record<string, string> = {
      "how-to-write-perfect-essay-guide": "Student writing an essay with pen and paper, surrounded by books and notes on a wooden desk",
      "top-universities-computer-science-2024": "Modern university campus with students walking between academic buildings and computer science facilities",
      "academic-research-credible-sources-guide": "Academic library with open books, research papers, and laptop computer for scholarly research"
    };

    // Update each blog with appropriate alt text
    for (const blog of blogs) {
      let altText = altTextMap[blog.slug];
      
      // If no specific alt text is defined, generate a generic one based on the title
      if (!altText) {
        altText = `Featured image for blog post: ${blog.title}`;
      }

      try {
        await prisma.blog.update({
          where: { id: blog.id },
          data: {
            imageAlt: altText,
          },
        });
        
      } catch (error) {
        console.error(`Error updating blog "${blog.title}":`, error);
      }
    }

    
  } catch (error) {
    console.error("Error updating blog posts:", error);
  }
}

main()
  .catch((e) => {
    console.error("Error updating blog posts:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
