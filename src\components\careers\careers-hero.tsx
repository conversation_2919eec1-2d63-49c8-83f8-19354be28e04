"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  DollarSign, 
  Clock, 
  Home, 
  TrendingUp,
  Users,
  Award,
  Globe,
  Briefcase
} from "lucide-react";

export function CareersHero() {
  const highlights = [
    { icon: DollarSign, text: "Competitive Rates", value: "$15-50/page" },
    { icon: Clock, text: "Flexible Hours", value: "Work Anytime" },
    { icon: Home, text: "Remote Work", value: "100% Online" },
    { icon: TrendingUp, text: "Growth Potential", value: "Unlimited" },
  ];

  return (
    <section className="relative py-20 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5" />
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          {/* Badge */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-6"
          >
            <Badge 
              variant="secondary" 
              className="px-4 py-2 text-sm font-medium bg-primary/10 text-primary border-primary/20"
            >
              <Briefcase className="w-4 h-4 mr-2" />
              Now Hiring Expert Writers
            </Badge>
          </motion.div>

          {/* Main Heading */}
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.1 }}
            className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-primary via-primary/80 to-primary/60 bg-clip-text text-transparent"
          >
            Join Our Team of
            <br />
            <span className="text-foreground">Expert Writers</span>
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-lg md:text-xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed"
          >
            Turn your academic expertise into a rewarding career. Work with students worldwide, 
            set your own schedule, and earn competitive rates while making a real difference in education.
          </motion.p>

          {/* Highlight Features */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-10"
          >
            {highlights.map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                className="flex flex-col items-center p-4 rounded-lg bg-card/50 border border-border/50 hover:border-primary/30 transition-colors"
              >
                <item.icon className="w-8 h-8 text-primary mb-2" />
                <span className="text-sm font-medium text-center mb-1">{item.text}</span>
                <span className="text-xs text-muted-foreground text-center">{item.value}</span>
              </motion.div>
            ))}
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <Link href="/register/writer">
              <Button 
                size="lg" 
                className="bg-primary text-primary-foreground hover:bg-primary/90 px-8 py-3 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <Users className="w-5 h-5 mr-2" />
                Apply Now
              </Button>
            </Link>
            
            <Link href="/contact-us">
              <Button 
                variant="outline" 
                size="lg"
                className="px-8 py-3 text-lg font-semibold border-2 hover:border-primary hover:text-primary transition-all duration-300"
              >
                Learn More
              </Button>
            </Link>
          </motion.div>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="mt-12 pt-8 border-t border-border/50"
          >
            <p className="text-sm text-muted-foreground mb-6">
              Join our growing community of successful academic writers
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
              <div>
                <div className="text-2xl md:text-3xl font-bold text-primary mb-1">500+</div>
                <div className="text-sm text-muted-foreground">Active Writers</div>
              </div>
              <div>
                <div className="text-2xl md:text-3xl font-bold text-primary mb-1">$2M+</div>
                <div className="text-sm text-muted-foreground">Paid to Writers</div>
              </div>
              <div>
                <div className="text-2xl md:text-3xl font-bold text-primary mb-1">150+</div>
                <div className="text-sm text-muted-foreground">Countries</div>
              </div>
              <div>
                <div className="text-2xl md:text-3xl font-bold text-primary mb-1">24/7</div>
                <div className="text-sm text-muted-foreground">Support</div>
              </div>
            </div>
          </motion.div>

          {/* Trust Indicators */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.7 }}
            className="mt-8 flex justify-center items-center gap-8 text-sm text-muted-foreground"
          >
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <span>Instant Payments</span>
            </div>
            <div className="flex items-center gap-2">
              <Globe className="w-4 h-4" />
              <span>Global Opportunities</span>
            </div>
            <div className="flex items-center gap-2">
              <Award className="w-4 h-4 text-primary" />
              <span>Professional Growth</span>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
