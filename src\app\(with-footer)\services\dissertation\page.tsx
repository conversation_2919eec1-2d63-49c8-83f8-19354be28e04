// src/app/(with-footer)/services/dissertation/page.tsx
import React from "react";
import { Metadata } from "next";
import { getRouteMetadata } from "@/lib/route-metadata";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  GraduationCap,
  BookOpen,
  FileText,
  Search,
  BarChart3,
  CheckCircle,
  Clock,
  Users,
  Award,
  Lightbulb,
  Database,
  Presentation, // Changed from PresentationChart
} from "lucide-react";

export async function generateMetadata(): Promise<Metadata> {
  return await getRouteMetadata("/services/dissertation");
}

const DissertationService: React.FC = () => {
  const dissertationStages = [
    {
      stage: "Proposal",
      progress: 15,
      description: "Research proposal and topic selection",
    },
    {
      stage: "Literature Review",
      progress: 35,
      description: "Comprehensive review of existing research",
    },
    {
      stage: "Methodology",
      progress: 50,
      description: "Research design and data collection methods",
    },
    {
      stage: "Data Analysis",
      progress: 75,
      description: "Statistical analysis and interpretation",
    },
    {
      stage: "Writing & Defense",
      progress: 100,
      description: "Complete dissertation and defense preparation",
    },
  ];

  const expertiseAreas = [
    "Business & Management",
    "Education & Psychology",
    "Healthcare & Nursing",
    "Engineering & Technology",
    "Social Sciences",
    "Literature & Humanities",
    "Law & Legal Studies",
    "Science & Research",
  ];

  const serviceFeatures = [
    {
      icon: <Search className="h-6 w-6" />,
      title: "Comprehensive Research",
      description:
        "Extensive literature review and primary research using latest methodologies",
    },
    {
      icon: <BarChart3 className="h-6 w-6" />,
      title: "Statistical Analysis",
      description:
        "Advanced statistical analysis using SPSS, R, or other specialized software",
    },
    {
      icon: <Database className="h-6 w-6" />,
      title: "Data Collection",
      description:
        "Professional data collection through surveys, interviews, and experiments",
    },
    {
      icon: <Presentation className="h-6 w-6" />, // Changed from PresentationChart
      title: "Defense Preparation",
      description:
        "Complete preparation materials for your dissertation defense",
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-12 max-w-6xl">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <Badge variant="secondary" className="mb-4">
            <GraduationCap className="h-4 w-4 mr-2" />
            PhD & Master&apos;s Level
          </Badge>
          <h1 className="text-4xl font-bold text-foreground mb-4">
            Professional Dissertation Writing Services
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Expert dissertation assistance from proposal to defense. Our
            PhD-qualified writers provide comprehensive support for your
            doctoral or master&apos;s dissertation journey.
          </p>
        </div>

        {/* What is a Dissertation */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-6 w-6 text-primary" />
              What is a Dissertation?
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground leading-relaxed mb-4">
              A dissertation is an extensive academic document that presents
              original research conducted by a graduate student as part of their
              doctoral or master&apos;s degree requirements. It represents the
              culmination of years of study and demonstrates the student&apos;s
              expertise in their chosen field through independent research.
            </p>
            <p className="text-muted-foreground leading-relaxed mb-4">
              Unlike essays or research papers, dissertations are substantial
              works typically ranging from 10,000 to 80,000 words, depending on
              the academic level and field of study. They require extensive
              literature review, original research methodology, data collection
              and analysis, and contribute new knowledge to the academic field.
            </p>
            <div className="bg-muted/50 p-4 rounded-lg">
              <p className="text-sm text-muted-foreground">
                <strong>Key Components:</strong> Abstract, Introduction,
                Literature Review, Methodology, Results/Findings, Discussion,
                Conclusion, References, and Appendices.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Dissertation Process */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>The Dissertation Journey</CardTitle>
            <CardDescription>
              Understanding the comprehensive process from start to finish
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {dissertationStages.map((item, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">{item.stage}</span>
                    <span className="text-sm text-muted-foreground">
                      {item.progress}%
                    </span>
                  </div>
                  <Progress value={item.progress} className="h-2" />
                  <p className="text-sm text-muted-foreground">
                    {item.description}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Service Tabs */}
        <Tabs defaultValue="services" className="mb-8">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="services" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300">Our Services</TabsTrigger>
            <TabsTrigger value="expertise" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300">Expertise Areas</TabsTrigger>
            <TabsTrigger value="process" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300">Our Process</TabsTrigger>
          </TabsList>

          <TabsContent value="services" className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              {serviceFeatures.map((feature, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardContent className="pt-6">
                    <div className="text-primary mb-3">{feature.icon}</div>
                    <h3 className="font-semibold mb-2">{feature.title}</h3>
                    <p className="text-sm text-muted-foreground">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="expertise" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Academic Fields We Cover</CardTitle>
                <CardDescription>
                  Our PhD writers specialize across diverse academic disciplines
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {expertiseAreas.map((area, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-2 p-3 rounded-lg bg-muted/50"
                    >
                      <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                      <span className="text-sm font-medium">{area}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="process" className="space-y-6">
            <div className="grid md:grid-cols-3 gap-6">
              <Card>
                <CardContent className="pt-6 text-center">
                  <Lightbulb className="h-8 w-8 text-primary mx-auto mb-3" />
                  <h4 className="font-semibold mb-2">1. Consultation</h4>
                  <p className="text-sm text-muted-foreground">
                    Initial discussion about your research topic, requirements,
                    and timeline
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="pt-6 text-center">
                  <Users className="h-8 w-8 text-primary mx-auto mb-3" />
                  <h4 className="font-semibold mb-2">2. Writer Matching</h4>
                  <p className="text-sm text-muted-foreground">
                    Assignment to a PhD writer with expertise in your specific
                    field
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="pt-6 text-center">
                  <FileText className="h-8 w-8 text-primary mx-auto mb-3" />
                  <h4 className="font-semibold mb-2">
                    3. Progressive Delivery
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    Chapter-by-chapter delivery with regular updates and
                    revisions
                  </p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Why Choose Us */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-6 w-6 text-primary" />
              Why Choose Our Dissertation Services?
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <Clock className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                  <div>
                    <h4 className="font-semibold mb-1">Flexible Timeline</h4>
                    <p className="text-sm text-muted-foreground">
                      We work with your schedule, offering support from urgent
                      deadlines to extended projects
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                  <div>
                    <h4 className="font-semibold mb-1">
                      Plagiarism-Free Guarantee
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      100% original research with comprehensive plagiarism
                      reports included
                    </p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <Users className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                  <div>
                    <h4 className="font-semibold mb-1">Direct Communication</h4>
                    <p className="text-sm text-muted-foreground">
                      Regular communication with your assigned writer throughout
                      the process
                    </p>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <div className="bg-primary/5 p-4 rounded-lg border border-primary/20">
                  <h4 className="font-semibold mb-2 text-primary">
                    Success Statistics
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Successful Defenses</span>
                      <span className="font-medium">98%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Client Satisfaction</span>
                      <span className="font-medium">99.2%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>On-Time Delivery</span>
                      <span className="font-medium">97%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* CTA Section */}
        <Card className="text-center bg-primary/5 border-primary/20">
          <CardContent className="pt-6">
            <h3 className="text-2xl font-bold mb-4">
              Start Your Dissertation Journey Today
            </h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Don&apos;t let your dissertation become overwhelming. Our expert
              writers are here to guide you through every step of the process,
              from initial proposal to final defense preparation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="px-8">
                Get Dissertation Help
              </Button>
              <Button size="lg" variant="outline" className="px-8">
                Free Consultation
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DissertationService;
