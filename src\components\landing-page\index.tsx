// src/components/landing-page/index.tsx
import React, { Suspense, lazy } from "react";


// Lazy load components for better performance
const HeroSection = lazy(() => import("./sections/HeroSection"));
const AboutSection = lazy(() => import("./sections/AboutSection"));
const ProgramsSection = lazy(() => import("./sections/ProgramsSection"));
const WhyChooseSection = lazy(() => import("./sections/WhyChooseSection"));
const TestimonialsSection = lazy(() => import("./sections/TestimonialsSection"));
const ContactSection = lazy(() => import("./sections/ContactSection"));
const WritersSection = lazy(() => import("./sections/WritersSection"));

// Loading placeholder
const SectionLoader = () => (
  <div className="w-full flex justify-center items-center">
    <div className="h-12 w-12 rounded-full border-4 border-b-transparent border-blue-600 animate-spin"></div>
  </div>
);

export const AcademicWritingLandingPage = () => {
  return (
    <div className="min-h-screen flex flex-col w-full">
      <main className="flex-grow px-2 sm:px-4 md:px-6 lg:px-8">
        <Suspense fallback={<SectionLoader />}>
          <HeroSection />
          <AboutSection />
          <WritersSection />
          <ProgramsSection />
          <WhyChooseSection />
          <TestimonialsSection />
          <ContactSection />
        </Suspense>
      </main>


    </div>
  );
};

export default AcademicWritingLandingPage;