// src/hooks/use-company-info.ts
import { useState, useEffect, useCallback } from "react";
import type { CompanyInfoResponse } from "@/types/api";

interface UseCompanyInfoReturn {
  companyInfo: CompanyInfoResponse | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

// Global state for company info
let globalCompanyInfo: CompanyInfoResponse | null = null;
let globalLoading = false;
let globalError: string | null = null;
let isInitialized = false;
const subscribers = new Set<() => void>();

// Function to notify all subscribers of state changes
const notifySubscribers = () => {
  subscribers.forEach(callback => callback());
};

// Global fetch function
const fetchCompanyInfoGlobal = async () => {
  try {
    globalLoading = true;
    globalError = null;
    notifySubscribers();

    const response = await fetch("/api/admin/company-info");
    const data = await response.json();

    if (response.ok && data.success) {
      globalCompanyInfo = data.data;
    } else {
      // If company info doesn't exist, set default values
      if (response.status === 404) {
        globalCompanyInfo = {
          id: "",
          companyName: "Essay App",
          address: "1234 Academic Way, Suite 500",
          city: "New York",
          state: "NY",
          zipCode: "10001",
          country: "United States",
          phone: "+****************",
          tollFreePhone: "+****************",
          internationalPhone: "+****************",
          supportEmail: "<EMAIL>",
          inquiriesEmail: "<EMAIL>",
          businessHours: "24/7 Customer Support - Business hours: Mon-Fri 9am-6pm EST",
          description: "Professional academic writing services for students at all levels. Get expert help with essays, research papers, dissertations, and more.",
          website: "https://essayapp.com",
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
      } else {
        globalError = data.message || "Failed to fetch company information";
      }
    }
  } catch (err) {
    console.error("Error fetching company info:", err);
    globalError = "Failed to fetch company information";

    // Set default values on error
    globalCompanyInfo = {
      id: "",
      companyName: "Essay App",
      address: "1234 Academic Way, Suite 500",
      city: "New York",
      state: "NY",
      zipCode: "10001",
      country: "United States",
      phone: "+****************",
      tollFreePhone: "+****************",
      internationalPhone: "+****************",
      supportEmail: "<EMAIL>",
      inquiriesEmail: "<EMAIL>",
      businessHours: "24/7 Customer Support - Business hours: Mon-Fri 9am-6pm EST",
      description: "Professional academic writing services for students at all levels. Get expert help with essays, research papers, dissertations, and more.",
      website: "https://essayscholars.us",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  } finally {
    globalLoading = false;
    notifySubscribers();
  }
};

export function useCompanyInfo(): UseCompanyInfoReturn {
  const [, forceUpdate] = useState({});

  // Force component re-render when global state changes
  const triggerUpdate = useCallback(() => {
    forceUpdate({});
  }, []);

  useEffect(() => {
    // Subscribe to global state changes
    subscribers.add(triggerUpdate);

    // Fetch data if not already initialized
    if (!isInitialized) {
      isInitialized = true;
      fetchCompanyInfoGlobal();
    }

    // Cleanup subscription on unmount
    return () => {
      subscribers.delete(triggerUpdate);
    };
  }, [triggerUpdate]);

  const refetch = useCallback(async () => {
    await fetchCompanyInfoGlobal();
  }, []);

  return {
    companyInfo: globalCompanyInfo,
    loading: globalLoading,
    error: globalError,
    refetch,
  };
}

// Hook specifically for admin settings with update functionality
interface UseAdminCompanyInfoReturn extends UseCompanyInfoReturn {
  updateCompanyInfo: (data: Partial<CompanyInfoResponse>) => Promise<boolean>;
  updating: boolean;
}

export function useAdminCompanyInfo(): UseAdminCompanyInfoReturn {
  const { companyInfo, loading, error, refetch } = useCompanyInfo();
  const [updating, setUpdating] = useState(false);

  const updateCompanyInfo = useCallback(async (data: Partial<CompanyInfoResponse>): Promise<boolean> => {
    try {
      setUpdating(true);

      // Filter out fields that shouldn't be sent to the API
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { id, createdAt, updatedAt, ...updateData } = data;

      console.log("Sending company info data:", updateData);

      const method = globalCompanyInfo?.id ? "PUT" : "POST";
      const response = await fetch("/api/admin/company-info", {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        await refetch(); // Refresh the global data
        return true;
      } else {
        console.error("Failed to update company info:", {
          status: response.status,
          message: result.message,
          errors: result.errors
        });
        return false;
      }
    } catch (err) {
      console.error("Error updating company info:", err);
      return false;
    } finally {
      setUpdating(false);
    }
  }, [refetch]);

  return {
    companyInfo,
    loading,
    error,
    refetch,
    updateCompanyInfo,
    updating,
  };
}


