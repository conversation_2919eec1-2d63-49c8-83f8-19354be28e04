# Quill Editor Components

This directory contains two Quill editor implementations for the academic app:

## Components

### 1. Traditional Quill Editor (`index.tsx`)
- Standard Quill editor with fixed toolbar at the top
- Uses custom shadcn/ui tooltips for better UX
- Supports all standard Quill formatting options
- Responsive design with proper mobile support

### 2. Headless Quill Editor (`headless-editor.tsx`)
- Modern implementation with floating toolbar
- Toolbar appears only when text is selected
- Context-aware and space-efficient design
- Responsive with mobile-optimized toolbar
- Smooth animations and transitions

## Features

### Headless Toolbar Benefits
- ✅ **Context-aware**: Toolbar appears only when needed
- ✅ **Space-efficient**: Saves vertical space in the interface
- ✅ **Modern UX**: Intuitive and clean user experience
- ✅ **Responsive**: Optimized for both desktop and mobile
- ✅ **Smart positioning**: Automatically stays within viewport
- ✅ **Smooth animations**: Polished appearance and transitions

### Available Formatting Options
- **Text formatting**: Bold, Italic, Underline, Strikethrough
- **Block elements**: Blockquote, Code blocks
- **Headings**: H1, H2 support
- **Lists**: Bullet lists, Numbered lists
- **Alignment**: Left, Center, Right, Justify
- **Media**: Links, Images, Videos
- **Interactive**: Close button to dismiss toolbar

## Usage

### Basic Implementation
```tsx
import HeadlessQuillEditor from "@/components/quill-editor/headless-editor";

function MyComponent() {
  const [content, setContent] = useState("");

  return (
    <HeadlessQuillEditor
      value={content}
      onChange={setContent}
      placeholder="Start typing..."
      className="min-h-[300px]"
      showHeadlessToolbar={true}
    />
  );
}
```

### Props

#### HeadlessQuillEditor Props
```tsx
interface HeadlessQuillEditorProps {
  value: string;                    // HTML content
  onChange: (value: string) => void; // Change handler
  className?: string;               // Additional CSS classes
  placeholder?: string;             // Placeholder text
  readOnly?: boolean;              // Read-only mode
  showHeadlessToolbar?: boolean;   // Enable/disable headless toolbar
}
```

## Implementation Details

### Responsive Design
- **Desktop**: Full toolbar with all formatting options
- **Mobile**: Condensed toolbar with essential tools only
- **Viewport constraints**: Toolbar automatically repositions to stay visible
- **Touch-friendly**: Larger touch targets on mobile devices

### Accessibility
- ARIA labels for all toolbar buttons
- Keyboard navigation support
- Screen reader compatible
- High contrast support

### Performance
- Lazy loading with dynamic imports
- Efficient re-rendering with React.memo patterns
- Optimized event handling with useCallback
- Minimal DOM manipulation

## Styling

### CSS Classes
- `.headless-quill`: Main container class
- `.headless-toolbar-enter`: Animation class for toolbar appearance
- Custom CSS variables for theming support
- Dark mode compatibility

### Customization
The editor supports full theming through CSS variables:
```css
.headless-quill {
  --editor-border: hsl(var(--border));
  --editor-focus: hsl(var(--ring));
  --editor-background: hsl(var(--background));
}
```

## Integration Examples

### Blog Management
Used in the admin blog management system:
- Create new blog posts (`/admin/blog`)
- Edit existing posts (`/admin/blog-management/edit/[id]`)
- Demo page (`/admin/editor-demo`)

### Form Integration
```tsx
// In a form component
<form onSubmit={handleSubmit}>
  <HeadlessQuillEditor
    value={formData.content}
    onChange={(content) => setFormData(prev => ({ ...prev, content }))}
    placeholder="Write your content here..."
  />
  <button type="submit">Save</button>
</form>
```

## Browser Support
- Chrome/Edge 88+
- Firefox 85+
- Safari 14+
- Mobile browsers with modern JavaScript support

## Dependencies
- `react-quill-new`: Quill React wrapper
- `quill`: Rich text editor core
- `lucide-react`: Icons for toolbar buttons
- `@/components/ui/*`: shadcn/ui components

## Troubleshooting

### Common Issues
1. **Toolbar not appearing**: Ensure `showHeadlessToolbar={true}` is set
2. **Positioning issues**: Check viewport constraints and CSS z-index
3. **Mobile responsiveness**: Verify touch event handling
4. **Styling conflicts**: Check CSS specificity and import order

### Debug Mode
Enable debug logging by setting:
```tsx
// Add to component for debugging
useEffect(() => {
  console.log('Toolbar visible:', toolbarVisible);
  console.log('Position:', toolbarPosition);
}, [toolbarVisible, toolbarPosition]);
```

## Future Enhancements
- [ ] Color picker integration
- [ ] Font family selection
- [ ] Table support
- [ ] Collaborative editing
- [ ] Markdown import/export
- [ ] Custom toolbar configurations
- [ ] Plugin system for extensions
