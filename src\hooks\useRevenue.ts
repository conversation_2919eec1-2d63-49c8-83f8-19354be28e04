import { useState, useEffect, useCallback } from 'react';
import { PaymentStatus } from '@prisma/client';

interface Assignment {
  id: string;
  taskId: string;
  title: string;
  price: number;
  paymentStatus: PaymentStatus;
  createdAt: string;
  updatedAt: string;
}

interface AssignmentsApiResponse {
  success: boolean;
  message: string;
  data: {
    assignments: Assignment[];
    totalCount: number;
    pagination: {
      total: number;
      page: number;
      limit: number;
      pages: number;
    };
  };
}

interface RevenueData {
  totalRevenue: number;
  monthlyRevenue: number;
  revenueGrowth: number;
  paidAssignments: number;
  averageOrderValue: number;
  revenueByMonth: Array<{ month: string; revenue: number; count: number }>;
}

interface UseRevenueReturn {
  revenueData: RevenueData;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useRevenue(): UseRevenueReturn {
  const [revenueData, setRevenueData] = useState<RevenueData>({
    totalRevenue: 0,
    monthlyRevenue: 0,
    revenueGrowth: 0,
    paidAssignments: 0,
    averageOrderValue: 0,
    revenueByMonth: [],
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const calculateRevenue = useCallback((assignments: Assignment[]): RevenueData => {
    // Filter only PAID assignments
    const paidAssignments = assignments.filter(
      (assignment) => assignment.paymentStatus === PaymentStatus.PAID
    );

    // Calculate total revenue
    const totalRevenue = paidAssignments.reduce(
      (sum, assignment) => sum + assignment.price,
      0
    );

    // Calculate current month revenue
    const currentDate = new Date();
    const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    
    const monthlyPaidAssignments = paidAssignments.filter(
      (assignment) => new Date(assignment.updatedAt) >= startOfMonth
    );
    
    const monthlyRevenue = monthlyPaidAssignments.reduce(
      (sum, assignment) => sum + assignment.price,
      0
    );

    // Calculate previous month revenue for growth comparison
    const startOfPrevMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
    const endOfPrevMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 0);
    
    const prevMonthPaidAssignments = paidAssignments.filter((assignment) => {
      const paymentDate = new Date(assignment.updatedAt);
      return paymentDate >= startOfPrevMonth && paymentDate <= endOfPrevMonth;
    });
    
    const prevMonthRevenue = prevMonthPaidAssignments.reduce(
      (sum, assignment) => sum + assignment.price,
      0
    );

    // Calculate revenue growth percentage
    const revenueGrowth = prevMonthRevenue > 0 
      ? ((monthlyRevenue - prevMonthRevenue) / prevMonthRevenue) * 100 
      : monthlyRevenue > 0 ? 100 : 0;

    // Calculate average order value
    const averageOrderValue = paidAssignments.length > 0 
      ? totalRevenue / paidAssignments.length 
      : 0;

    // Group revenue by month for the last 12 months
    const revenueByMonth: Array<{ month: string; revenue: number; count: number }> = [];
    
    for (let i = 11; i >= 0; i--) {
      const monthDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      const nextMonthDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - i + 1, 1);
      
      const monthAssignments = paidAssignments.filter((assignment) => {
        const paymentDate = new Date(assignment.updatedAt);
        return paymentDate >= monthDate && paymentDate < nextMonthDate;
      });
      
      const monthRevenue = monthAssignments.reduce(
        (sum, assignment) => sum + assignment.price,
        0
      );
      
      revenueByMonth.push({
        month: monthDate.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        revenue: monthRevenue,
        count: monthAssignments.length,
      });
    }

    return {
      totalRevenue,
      monthlyRevenue,
      revenueGrowth,
      paidAssignments: paidAssignments.length,
      averageOrderValue,
      revenueByMonth,
    };
  }, []);

  const fetchRevenue = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch all assignments with a high limit to get complete data
      const response = await fetch('/api/assignments?limit=10000');
      const data: AssignmentsApiResponse = await response.json();

      if (!data.success || !data.data?.assignments) {
        throw new Error('Failed to fetch assignments data');
      }

      const calculatedRevenue = calculateRevenue(data.data.assignments);
      setRevenueData(calculatedRevenue);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch revenue data';
      setError(errorMessage);
      console.error('Error fetching revenue data:', err);
    } finally {
      setLoading(false);
    }
  }, [calculateRevenue]);

  useEffect(() => {
    fetchRevenue();
  }, [fetchRevenue]);

  return {
    revenueData,
    loading,
    error,
    refetch: fetchRevenue,
  };
}
