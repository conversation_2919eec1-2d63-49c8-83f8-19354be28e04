import React from 'react';
import { <PERSON>ada<PERSON> } from "next";
import { getRouteMetadata } from "@/lib/route-metadata";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
// import { ScrollArea } from '@/components/ui/scroll-area';
import { Shield, FileText, Users, CreditCard, AlertTriangle, Mail } from 'lucide-react';

export async function generateMetadata(): Promise<Metadata> {
  return await getRouteMetadata("/terms");
}

const TermsAndConditions = () => {
  const sections = [
    {
      id: 'acceptance',
      title: 'Acceptance of Terms',
      icon: <FileText className="w-5 h-5" />,
      content: `
        By accessing and using our academic writing platform, you acknowledge that you have read, understood, and agree to be bound by these Terms and Conditions. If you do not agree with any part of these terms, you must not use our services.
        
        These terms constitute a legally binding agreement between you and our platform. We reserve the right to modify these terms at any time, and continued use of the platform constitutes acceptance of any changes.
      `
    },
    {
      id: 'services',
      title: 'Description of Services',
      icon: <Users className="w-5 h-5" />,
      content: `
        Our platform serves as a marketplace connecting students who need academic writing assistance with qualified writers. We provide:
        
        • A secure bidding system for assignments
        • Communication tools between clients and writers
        • Payment processing and escrow services
        • Quality assurance and dispute resolution
        • User verification and rating systems
        
        We act solely as an intermediary and do not create, review, or guarantee the quality of work produced by writers on our platform.
      `
    },
    {
      id: 'user-responsibilities',
      title: 'User Responsibilities',
      icon: <Shield className="w-5 h-5" />,
      content: `
        All users must:
        
        • Provide accurate and complete registration information
        • Maintain the confidentiality of account credentials
        • Use the platform only for lawful purposes
        • Respect intellectual property rights
        • Comply with academic integrity policies of their institutions
        • Report any suspicious or fraudulent activity
        
        Students are responsible for understanding their institution's policies regarding academic assistance and must use our services in compliance with those policies.
      `
    },
    {
      id: 'writer-obligations',
      title: 'Writer Obligations',
      icon: <Users className="w-5 h-5" />,
      content: `
        Writers on our platform must:
        
        • Deliver original, plagiarism-free content
        • Meet agreed-upon deadlines and specifications
        • Maintain professional communication standards
        • Provide accurate qualifications and experience information
        • Respect client confidentiality
        • Not subcontract work without explicit permission
        
        Writers are independent contractors and are responsible for their own tax obligations and professional conduct.
      `
    },
    {
      id: 'payment-terms',
      title: 'Payment and Billing',
      icon: <CreditCard className="w-5 h-5" />,
      content: `
        Payment Terms:
        
        • All payments are processed through secure, third-party payment processors
        • Funds are held in escrow until work completion and approval
        • Our platform charges a service fee on all transactions
        • Refunds are subject to our refund policy and dispute resolution process
        • Users are responsible for any applicable taxes
        
        Late payments may result in account suspension. All prices are quoted in USD unless otherwise specified.
      `
    },
    {
      id: 'intellectual-property',
      title: 'Intellectual Property',
      icon: <Shield className="w-5 h-5" />,
      content: `
        Content Ownership:
        
        • Writers retain rights to their original methodologies and approaches
        • Completed work becomes the property of the client upon full payment
        • Our platform retains rights to user-generated content for operational purposes
        • Users may not reproduce, distribute, or create derivative works without permission
        
        We respect intellectual property rights and will respond to valid DMCA notices in accordance with applicable law.
      `
    },
    {
      id: 'prohibited-conduct',
      title: 'Prohibited Conduct',
      icon: <AlertTriangle className="w-5 h-5" />,
      content: `
        The following activities are strictly prohibited:
        
        • Plagiarism or submission of non-original content
        • Harassment, threats, or abusive behavior
        • Attempting to circumvent our payment system
        • Creating multiple accounts to manipulate ratings
        • Sharing login credentials or account access
        • Posting false or misleading information
        • Using the platform for illegal activities
        
        Violation of these terms may result in immediate account termination and legal action.
      `
    },
    {
      id: 'privacy-data',
      title: 'Privacy and Data Protection',
      icon: <Shield className="w-5 h-5" />,
      content: `
        We are committed to protecting your privacy:
        
        • Personal information is collected and used in accordance with our Privacy Policy
        • We implement industry-standard security measures
        • Data is not sold to third parties without consent
        • Users have rights to access, correct, and delete their personal data
        • We comply with applicable data protection regulations
        
        Please review our separate Privacy Policy for detailed information about data handling practices.
      `
    },
    {
      id: 'disclaimers',
      title: 'Disclaimers and Limitations',
      icon: <AlertTriangle className="w-5 h-5" />,
      content: `
        Important Disclaimers:
        
        • Services are provided "as is" without warranties of any kind
        • We do not guarantee the quality, accuracy, or timeliness of work
        • Users assume full responsibility for how they use completed work
        • We are not liable for academic consequences resulting from service use
        • Our liability is limited to the amount paid for services
        
        Some jurisdictions may not allow certain disclaimer limitations, so some restrictions may not apply to you.
      `
    },
    {
      id: 'termination',
      title: 'Account Termination',
      icon: <AlertTriangle className="w-5 h-5" />,
      content: `
        Account Termination Conditions:
        
        • Either party may terminate the agreement at any time
        • We reserve the right to suspend or terminate accounts for policy violations
        • Upon termination, access to the platform and services will cease
        • Outstanding payments will be processed according to our payment terms
        • Certain provisions of these terms will survive termination
        
        We will provide reasonable notice before termination unless immediate action is required for security or legal reasons.
      `
    },
    {
      id: 'dispute-resolution',
      title: 'Dispute Resolution',
      icon: <Users className="w-5 h-5" />,
      content: `
        Dispute Resolution Process:
        
        • Initial disputes should be addressed through our internal resolution system
        • Mediation services are available for unresolved conflicts
        • Binding arbitration may be required for certain disputes
        • Class action lawsuits are waived where legally permissible
        • Governing law is determined by our jurisdiction of incorporation
        
        We encourage users to contact our support team first to resolve any issues amicably.
      `
    },
    {
      id: 'contact',
      title: 'Contact Information',
      icon: <Mail className="w-5 h-5" />,
      content: `
        For questions about these Terms and Conditions, please contact us:
        
        • Email: <EMAIL>
        • Support Portal: Available through your account dashboard
        • Mailing Address: [Your Business Address]
        • Business Hours: Monday - Friday, 9 AM - 6 PM EST
        
        We strive to respond to all inquiries within 24-48 hours during business days.
      `
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-card border-b">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl font-bold text-foreground mb-4">
              Terms and Conditions
            </h1>
            <p className="text-lg text-muted-foreground mb-2">
              Last updated: {new Date().toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </p>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Please read these terms carefully before using our academic writing platform. 
              These terms govern your use of our services and establish the rights and responsibilities of all parties.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Quick Navigation */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Quick Navigation
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {sections.map((section) => (
                  <a
                    key={section.id}
                    href={`#${section.id}`}
                    className="flex items-center gap-2 p-3 rounded-lg border border-border hover:bg-accent hover:text-accent-foreground transition-colors"
                  >
                    {section.icon}
                    <span className="text-sm font-medium">{section.title}</span>
                  </a>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Terms Sections */}
          <div className="space-y-6">
            {sections.map((section, index) => (
              <Card key={section.id} id={section.id} className="scroll-mt-20">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3 text-xl">
                    <div className="p-2 rounded-lg bg-primary/10 text-primary">
                      {section.icon}
                    </div>
                    <div>
                      <span className="text-sm text-muted-foreground font-normal">
                        Section {index + 1}
                      </span>
                      <div>{section.title}</div>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-gray dark:prose-invert max-w-none">
                    {section.content.split('\n\n').map((paragraph, pIndex) => (
                      <div key={pIndex} className="mb-4 last:mb-0">
                        {paragraph.includes('•') ? (
                          <ul className="list-disc list-inside space-y-2 text-muted-foreground">
                            {paragraph.split('•').slice(1).map((item, iIndex) => (
                              <li key={iIndex} className="text-foreground">
                                {item.trim()}
                              </li>
                            ))}
                          </ul>
                        ) : (
                          <p className="text-foreground leading-relaxed">
                            {paragraph.trim()}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Footer Notice */}
          <Card className="mt-8 bg-muted/50">
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <div className="flex items-center justify-center gap-2 text-primary">
                  <AlertTriangle className="w-5 h-5" />
                  <span className="font-medium">Important Notice</span>
                </div>
                <p className="text-sm text-muted-foreground max-w-2xl mx-auto">
                  These terms and conditions are subject to change. We will notify users of any material 
                  changes via email or platform notifications. Continued use of our services after changes 
                  are posted constitutes acceptance of the revised terms.
                </p>
                <Separator className="my-4" />
                <p className="text-xs text-muted-foreground">
                  If you have questions about these terms, please contact our legal team before using our services.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default TermsAndConditions;