import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authConfig } from "@/auth";
import { UserRole } from "@prisma/client";

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const intendedRole = searchParams.get("intended_role") as UserRole;
    const callbackUrl = searchParams.get("callbackUrl");

    // Get the current session
    const session = await getServerSession(authConfig);

    if (!session?.user?.email) {
      return NextResponse.redirect(new URL("/login/client", req.url));
    }

    // If no intended role specified, allow the login
    if (!intendedRole) {
      const dashboardUrl = getDashboardUrl(session.user.role as UserRole);
      return NextResponse.redirect(new URL(dashboardUrl, req.url));
    }

    // Check if user's actual role matches intended role
    if (session.user.role !== intendedRole) {
      const correctLoginPage = getCorrectLoginPage(session.user.role as UserRole);
      const roleName = getRoleName(session.user.role as UserRole);
      
      // Redirect to an error page with the message
      const errorUrl = new URL("/auth-error", req.url);
      errorUrl.searchParams.set("error", "WrongRole");
      errorUrl.searchParams.set("message", `Please login through the ${roleName} dashboard at ${correctLoginPage}`);
      errorUrl.searchParams.set("correctUrl", correctLoginPage);
      
      return NextResponse.redirect(errorUrl);
    }

    // Role matches - redirect to appropriate dashboard
    const dashboardUrl = getDashboardUrl(intendedRole);
    return NextResponse.redirect(new URL(callbackUrl || dashboardUrl, req.url));

  } catch (error) {
    console.error("Callback handler error:", error);
    return NextResponse.redirect(new URL("/auth-error", req.url));
  }
}

function getDashboardUrl(role: UserRole): string {
  switch (role) {
    case "ADMIN":
      return "/admin/dashboard";
    case "WRITER":
      return "/writer/dashboard";
    case "CLIENT":
      return "/client/dashboard";
    default:
      return "/client/dashboard";
  }
}

function getCorrectLoginPage(role: UserRole): string {
  switch (role) {
    case "ADMIN":
      return "/login/admin";
    case "WRITER":
      return "/login/writer";
    case "CLIENT":
      return "/login/client";
    default:
      return "/login/client";
  }
}

function getRoleName(role: UserRole): string {
  switch (role) {
    case "ADMIN":
      return "Admin";
    case "WRITER":
      return "Writer";
    case "CLIENT":
      return "Client";
    default:
      return "Client";
  }
}
