"use client";

import React, { useState, useEffect, useMemo, useCallback } from "react";
import { <PERSON>, CardH<PERSON>er, CardT<PERSON>le, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import {
  DollarSign,
  TrendingUp,
  Clock,
  FileText,
  Users,
  Calculator,
  Info,
  RefreshCw
} from "lucide-react";
import { AcademicLevel, Priority, Spacing } from "@prisma/client";

interface PricingRule {
  id: string;
  ruleType: string;
  academicLevel?: AcademicLevel;
  priority?: Priority;
  spacing?: Spacing;
  value: number;
  isActive: boolean;
}

interface PricePreview {
  basePrice: number;
  academicLevelMultiplier: number;
  priorityMultiplier: number;
  spacingMultiplier: number;
  subtotal: number;
  finalPrice: number;
  minimumPrice: number;
}

interface WriterCompensation {
  percentage: number;
  minimumPerPage: number;
  calculatedAmount: number;
  finalAmount: number;
}

export function PricingSettings() {
  const [pricingRules, setPricingRules] = useState<PricingRule[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [previewLoading, setPreviewLoading] = useState(false);
  
  // Preview calculation state
  const [previewParams, setPreviewParams] = useState<{
    academicLevel: AcademicLevel;
    priority: Priority;
    spacing: Spacing;
    pageCount: number;
  }>({
    academicLevel: AcademicLevel.MASTERS,
    priority: Priority.MEDIUM,
    spacing: Spacing.DOUBLE,
    pageCount: 5,
  });
  
  const [pricePreview, setPricePreview] = useState<PricePreview | null>(null);
  const [writerCompensation, setWriterCompensation] = useState<WriterCompensation | null>(null);

  // Fetch pricing rules
  const fetchPricingRules = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/admin/pricing");
      if (!response.ok) throw new Error("Failed to fetch pricing rules");
      
      const data = await response.json();
      setPricingRules(data.data || []);
    } catch (error) {
      console.error("Error fetching pricing rules:", error);
      toast.error("Failed to load pricing rules");
    } finally {
      setLoading(false);
    }
  };

  // Calculate price preview
  const calculatePreview = useCallback(async () => {
    try {
      setPreviewLoading(true);
      const response = await fetch("/api/admin/pricing", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(previewParams),
      });

      if (!response.ok) throw new Error("Failed to calculate preview");

      const data = await response.json();
      setPricePreview(data.data.priceBreakdown);
      setWriterCompensation(data.data.writerCompensation);
    } catch (error) {
      console.error("Error calculating preview:", error);
      toast.error("Failed to calculate price preview");
    } finally {
      setPreviewLoading(false);
    }
  }, [previewParams]);

  // Update pricing rule
  const updatePricingRule = async (ruleType: string, value: number, academicLevel?: AcademicLevel, priority?: Priority, spacing?: Spacing) => {
    try {
      setSaving(true);
      const response = await fetch("/api/admin/pricing", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ruleType,
          value,
          academicLevel,
          priority,
          spacing,
        }),
      });
      
      if (!response.ok) throw new Error("Failed to update pricing rule");
      
      toast.success("Pricing rule updated successfully");
      await fetchPricingRules();
      await calculatePreview();

      // Trigger real-time updates across all components
      if (typeof window !== 'undefined') {
        const windowWithTrigger = window as Window & { triggerPricingUpdate?: () => void };
        if (windowWithTrigger.triggerPricingUpdate) {
          windowWithTrigger.triggerPricingUpdate();
        }
      }
    } catch (error) {
      console.error("Error updating pricing rule:", error);
      toast.error("Failed to update pricing rule");
    } finally {
      setSaving(false);
    }
  };

  // Get rule value by type and parameters
  const getRuleValue = useCallback((ruleType: string, academicLevel?: AcademicLevel, priority?: Priority, spacing?: Spacing): number => {
    const rule = pricingRules.find(r => {
      // Handle undefined vs null comparison
      const academicMatch = (academicLevel === undefined && (r.academicLevel === null || r.academicLevel === undefined)) ||
                           r.academicLevel === academicLevel;
      const priorityMatch = (priority === undefined && (r.priority === null || r.priority === undefined)) ||
                           r.priority === priority;
      const spacingMatch = (spacing === undefined && (r.spacing === null || r.spacing === undefined)) ||
                          r.spacing === spacing;

      return r.ruleType === ruleType && academicMatch && priorityMatch && spacingMatch;
    });
    return rule?.value || 0;
  }, [pricingRules]);

  useEffect(() => {
    fetchPricingRules();
  }, []);

  useEffect(() => {
    if (pricingRules.length > 0) {
      calculatePreview();
    }
  }, [pricingRules, previewParams, calculatePreview]);

  // Organize rules for display
  const organizedRules = useMemo(() => {
    return {
      basePrice: getRuleValue("base_price"),
      minimumPrice: getRuleValue("minimum_price"),
      writerPercentage: getRuleValue("writer_percentage"),
      writerMinimumPerPage: getRuleValue("writer_minimum_per_page"),
      academicMultipliers: {
        [AcademicLevel.HIGH_SCHOOL]: getRuleValue("academic_multiplier", AcademicLevel.HIGH_SCHOOL),
        [AcademicLevel.UNDERGRADUATE]: getRuleValue("academic_multiplier", AcademicLevel.UNDERGRADUATE),
        [AcademicLevel.MASTERS]: getRuleValue("academic_multiplier", AcademicLevel.MASTERS),
        [AcademicLevel.PHD]: getRuleValue("academic_multiplier", AcademicLevel.PHD),
        [AcademicLevel.PROFESSIONAL]: getRuleValue("academic_multiplier", AcademicLevel.PROFESSIONAL),
      },
      priorityMultipliers: {
        [Priority.LOW]: getRuleValue("priority_multiplier", undefined, Priority.LOW),
        [Priority.MEDIUM]: getRuleValue("priority_multiplier", undefined, Priority.MEDIUM),
        [Priority.HIGH]: getRuleValue("priority_multiplier", undefined, Priority.HIGH),
      },
      spacingMultipliers: {
        [Spacing.DOUBLE]: getRuleValue("spacing_multiplier", undefined, undefined, Spacing.DOUBLE),
        [Spacing.SINGLE]: getRuleValue("spacing_multiplier", undefined, undefined, Spacing.SINGLE),
      },
    };
  }, [getRuleValue]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin" />
        <span className="ml-2">Loading pricing settings...</span>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div>
            <h2 className="text-xl sm:text-2xl font-bold tracking-tight">Pricing Strategy</h2>
            <p className="text-muted-foreground text-sm sm:text-base">
              Configure pricing rules and multipliers for your academic writing platform
            </p>
          </div>
          <Button onClick={fetchPricingRules} variant="outline" size="sm" className="w-full sm:w-auto">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>

        <div className="grid gap-6 xl:grid-cols-2">
          {/* Pricing Configuration */}
          <div className="space-y-6">
            {/* Base Pricing */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Base Pricing
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="basePrice">Base Price per Page (USD)</Label>
                    <div className="flex gap-2">
                      <Input
                        id="basePrice"
                        type="number"
                        step="0.01"
                        min="0"
                        value={organizedRules.basePrice}
                        onChange={(e) => updatePricingRule("base_price", parseFloat(e.target.value) || 0)}
                        disabled={saving}
                      />
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="outline" size="icon">
                            <Info className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>The base price charged per page before applying multipliers</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="minimumPrice">Minimum Order Price (USD)</Label>
                    <div className="flex gap-2">
                      <Input
                        id="minimumPrice"
                        type="number"
                        step="0.01"
                        min="0"
                        value={organizedRules.minimumPrice}
                        onChange={(e) => updatePricingRule("minimum_price", parseFloat(e.target.value) || 0)}
                        disabled={saving}
                      />
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="outline" size="icon">
                            <Info className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Minimum price for any order, regardless of page count</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Academic Level Multipliers */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Academic Level Multipliers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(organizedRules.academicMultipliers).map(([level, multiplier]) => (
                    <div key={level} className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3">
                      <Label className="w-full sm:w-32 text-sm font-medium">{level.replace('_', ' ')}</Label>
                      <div className="flex items-center gap-2 w-full sm:w-auto">
                        <Input
                          type="number"
                          step="0.1"
                          min="0.1"
                          value={multiplier}
                          onChange={(e) => updatePricingRule("academic_multiplier", parseFloat(e.target.value) || 1, level as AcademicLevel)}
                          disabled={saving}
                          className="w-20 sm:w-24"
                        />
                        <span className="text-sm text-muted-foreground">×</span>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-6 w-6 flex-shrink-0">
                              <Info className="h-3 w-3" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Multiplier applied based on academic complexity</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Priority Multipliers */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Priority Multipliers (Rush Orders)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3">
                    <Label className="w-full sm:w-32 text-sm font-medium">Standard (7+ days)</Label>
                    <div className="flex items-center gap-2 w-full sm:w-auto">
                      <Input
                        type="number"
                        step="0.1"
                        min="0.1"
                        value={organizedRules.priorityMultipliers[Priority.LOW]}
                        onChange={(e) => updatePricingRule("priority_multiplier", parseFloat(e.target.value) || 1, undefined, Priority.LOW)}
                        disabled={saving}
                        className="w-20 sm:w-24"
                      />
                      <span className="text-sm text-muted-foreground">×</span>
                      <Badge variant="secondary" className="bg-green-100 text-green-800 flex-shrink-0">Standard</Badge>
                    </div>
                  </div>
                  <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3">
                    <Label className="w-full sm:w-32 text-sm font-medium">Priority (3-6 days)</Label>
                    <div className="flex items-center gap-2 w-full sm:w-auto">
                      <Input
                        type="number"
                        step="0.1"
                        min="0.1"
                        value={organizedRules.priorityMultipliers[Priority.MEDIUM]}
                        onChange={(e) => updatePricingRule("priority_multiplier", parseFloat(e.target.value) || 1, undefined, Priority.MEDIUM)}
                        disabled={saving}
                        className="w-20 sm:w-24"
                      />
                      <span className="text-sm text-muted-foreground">×</span>
                      <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 flex-shrink-0">Priority</Badge>
                    </div>
                  </div>
                  <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3">
                    <Label className="w-full sm:w-32 text-sm font-medium">Urgent (1-2 days)</Label>
                    <div className="flex items-center gap-2 w-full sm:w-auto">
                      <Input
                        type="number"
                        step="0.1"
                        min="0.1"
                        value={organizedRules.priorityMultipliers[Priority.HIGH]}
                        onChange={(e) => updatePricingRule("priority_multiplier", parseFloat(e.target.value) || 1, undefined, Priority.HIGH)}
                        disabled={saving}
                        className="w-20 sm:w-24"
                      />
                      <span className="text-sm text-muted-foreground">×</span>
                      <Badge variant="secondary" className="bg-red-100 text-red-800 flex-shrink-0">Urgent</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Spacing Multipliers */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Spacing Multipliers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Label className="w-32 text-sm">Double Spaced</Label>
                    <Input
                      type="number"
                      step="0.1"
                      min="0.1"
                      value={organizedRules.spacingMultipliers[Spacing.DOUBLE]}
                      onChange={(e) => updatePricingRule("spacing_multiplier", parseFloat(e.target.value) || 1, undefined, undefined, Spacing.DOUBLE)}
                      disabled={saving}
                      className="w-24"
                    />
                    <span className="text-sm text-muted-foreground">×</span>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-6 w-6">
                          <Info className="h-3 w-3" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Default spacing - standard content density</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <div className="flex items-center gap-3">
                    <Label className="w-32 text-sm">Single Spaced</Label>
                    <Input
                      type="number"
                      step="0.1"
                      min="0.1"
                      value={organizedRules.spacingMultipliers[Spacing.SINGLE]}
                      onChange={(e) => updatePricingRule("spacing_multiplier", parseFloat(e.target.value) || 1, undefined, undefined, Spacing.SINGLE)}
                      disabled={saving}
                      className="w-24"
                    />
                    <span className="text-sm text-muted-foreground">×</span>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-6 w-6">
                          <Info className="h-3 w-3" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Single spacing has twice the content of double spacing</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Writer Compensation */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Writer Compensation
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="writerPercentage">Writer Percentage (%)</Label>
                  <div className="flex gap-2">
                    <Input
                      id="writerPercentage"
                      type="number"
                      step="0.01"
                      min="0"
                      max="1"
                      value={organizedRules.writerPercentage}
                      onChange={(e) => updatePricingRule("writer_percentage", parseFloat(e.target.value) || 0)}
                      disabled={saving}
                    />
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="outline" size="icon">
                          <Info className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Percentage of client payment that goes to the writer (0.35 = 35%)</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="writerMinimum">Minimum per Page (USD)</Label>
                  <div className="flex gap-2">
                    <Input
                      id="writerMinimum"
                      type="number"
                      step="0.01"
                      min="0"
                      value={organizedRules.writerMinimumPerPage}
                      onChange={(e) => updatePricingRule("writer_minimum_per_page", parseFloat(e.target.value) || 0)}
                      disabled={saving}
                    />
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="outline" size="icon">
                          <Info className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Minimum amount per page that writers receive</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                </div>

                {/* Writer Compensation Preview */}
                {writerCompensation && pricePreview && (
                  <div className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                    <h5 className="font-medium text-sm mb-2">Writer Compensation Preview:</h5>
                    <div className="space-y-1 text-xs">
                      <div className="flex justify-between">
                        <span>Percentage ({(writerCompensation.percentage * 100).toFixed(1)}%)</span>
                        <span>${writerCompensation.calculatedAmount.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Minimum ({previewParams.pageCount} × ${writerCompensation.minimumPerPage})</span>
                        <span>${(previewParams.pageCount * writerCompensation.minimumPerPage).toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between font-medium pt-1 border-t">
                        <span>Writer Gets</span>
                        <span>${writerCompensation.finalAmount.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between text-muted-foreground">
                        <span>Platform Profit</span>
                        <span>${(pricePreview.finalPrice - writerCompensation.finalAmount).toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Real-time Preview */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calculator className="h-5 w-5" />
                  Price Preview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Preview Controls */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Academic Level</Label>
                    <Select
                      value={previewParams.academicLevel}
                      onValueChange={(value) => setPreviewParams(prev => ({ ...prev, academicLevel: value as AcademicLevel }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(AcademicLevel).map(level => (
                          <SelectItem key={level} value={level}>
                            {level.replace('_', ' ')}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Priority</Label>
                    <Select
                      value={previewParams.priority}
                      onValueChange={(value) => setPreviewParams(prev => ({ ...prev, priority: value as Priority }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={Priority.LOW}>Standard (7+ days)</SelectItem>
                        <SelectItem value={Priority.MEDIUM}>Priority (3-6 days)</SelectItem>
                        <SelectItem value={Priority.HIGH}>Urgent (1-2 days)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Spacing</Label>
                    <Select
                      value={previewParams.spacing}
                      onValueChange={(value) => setPreviewParams(prev => ({ ...prev, spacing: value as Spacing }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={Spacing.DOUBLE}>Double Spaced</SelectItem>
                        <SelectItem value={Spacing.SINGLE}>Single Spaced</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Page Count</Label>
                    <Input
                      type="number"
                      min="1"
                      value={previewParams.pageCount}
                      onChange={(e) => setPreviewParams(prev => ({ ...prev, pageCount: parseInt(e.target.value) || 1 }))}
                    />
                  </div>
                </div>

                {/* Price Breakdown */}
                {pricePreview && !previewLoading && (
                  <div className="space-y-3 p-4 bg-muted/50 rounded-lg">
                    <h4 className="font-semibold">Price Breakdown:</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Base Price ({previewParams.pageCount} pages)</span>
                        <span>${pricePreview.basePrice.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Academic Level (×{pricePreview.academicLevelMultiplier})</span>
                        <span>${(pricePreview.basePrice * pricePreview.academicLevelMultiplier - pricePreview.basePrice).toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Priority (×{pricePreview.priorityMultiplier})</span>
                        <span>${(pricePreview.basePrice * pricePreview.academicLevelMultiplier * pricePreview.priorityMultiplier - pricePreview.basePrice * pricePreview.academicLevelMultiplier).toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Spacing (×{pricePreview.spacingMultiplier})</span>
                        <span>${(pricePreview.subtotal - pricePreview.basePrice * pricePreview.academicLevelMultiplier * pricePreview.priorityMultiplier).toFixed(2)}</span>
                      </div>
                      <Separator />
                      <div className="flex justify-between font-semibold">
                        <span>Final Price</span>
                        <span>${pricePreview.finalPrice.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                )}

                {previewLoading && (
                  <div className="flex items-center justify-center p-4">
                    <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                    <span>Calculating...</span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Pricing Strategy Explanation */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="h-5 w-5" />
              Pricing Strategy Logic
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  <strong>How Pricing Works:</strong> The final price is calculated by multiplying the base price per page by the page count,
                  then applying academic level, priority, and spacing multipliers in sequence. The result is compared to the minimum price,
                  and the higher value is used as the final price.
                </AlertDescription>
              </Alert>

              <div className="grid gap-4 sm:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3">
                <div className="space-y-3">
                  <h4 className="font-semibold">Academic Level Multipliers</h4>
                  <div className="text-sm space-y-1">
                    <p><strong>High School (1.0×):</strong> Basic academic writing requirements</p>
                    <p><strong>Undergraduate (1.2×):</strong> Standard college-level complexity</p>
                    <p><strong>Masters (1.5×):</strong> Advanced research and analysis required</p>
                    <p><strong>PhD (2.0×):</strong> Highest academic rigor and expertise needed</p>
                    <p><strong>Professional (1.8×):</strong> Industry-specific professional writing</p>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-semibold">Priority Multipliers</h4>
                  <div className="text-sm space-y-1">
                    <p><strong>Standard (1.0×):</strong> 7+ days delivery time</p>
                    <p><strong>Priority (1.5×):</strong> 3-6 days delivery time</p>
                    <p><strong>Urgent (2.0×):</strong> 1-2 days delivery time</p>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-semibold">Spacing Logic</h4>
                  <div className="text-sm space-y-1">
                    <p><strong>Double Spaced (1.0×):</strong> Standard formatting with normal content density</p>
                    <p><strong>Single Spaced (2.0×):</strong> Twice the content per page compared to double spacing</p>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-semibold">Writer Compensation</h4>
                  <div className="text-sm space-y-1">
                    <p><strong>Percentage Model:</strong> Writers receive a percentage of the client payment</p>
                    <p><strong>Minimum Protection:</strong> Ensures writers get at least the minimum per-page rate</p>
                    <p><strong>Final Amount:</strong> Higher of percentage calculation or minimum rate</p>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-muted/50 rounded-lg">
                <h4 className="font-semibold mb-2">Example Calculation:</h4>
                <div className="text-sm space-y-1">
                  <p>5-page Masters essay, Priority delivery, Double spaced:</p>
                  <p>1. Base Price: 5 pages × $15 = $75</p>
                  <p>2. Academic Level: $75 × 1.5 (Masters) = $112.50</p>
                  <p>3. Priority: $112.50 × 1.5 (Priority) = $168.75</p>
                  <p>4. Spacing: $168.75 × 1.0 (Double) = $168.75</p>
                  <p><strong>Final Price: $168.75</strong></p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  );
}
