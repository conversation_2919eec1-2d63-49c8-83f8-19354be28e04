import { Metadata } from "next";
import { getRouteMetadata } from "@/lib/route-metadata";
import { AdminLoginForm } from "@/components/admin-login-form";

export async function generateMetadata(): Promise<Metadata> {
  return await getRouteMetadata("/login/admin");
}

export default function LoginPage() {
  return (
    <div className="container flex h-screen w-full items-center justify-center px-4 py-8">
      <AdminLoginForm className="w-full max-w-4xl" />
    </div>
  );
}
