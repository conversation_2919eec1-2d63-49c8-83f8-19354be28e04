import { Metada<PERSON> } from "next";
import { getRouteMetadata } from "@/lib/route-metadata";
import { LoginForm } from "@/components/login-form"

export async function generateMetadata(): Promise<Metadata> {
  return await getRouteMetadata("/login/client");
}

export default function LoginPage() {
  return (
    <div className="container flex h-screen w-full items-center justify-center px-4 py-8">
      <LoginForm className="w-full max-w-4xl" />
    </div>
  )
}