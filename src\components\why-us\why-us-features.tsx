"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  GraduationCap, 
  Clock, 
  Shield, 
  Star,
  Users,
  FileCheck,
  Headphones,
  Award,
  RefreshCw,
  Lock,
  Zap,
  Heart
} from "lucide-react";

const features = [
  {
    icon: GraduationCap,
    title: "Expert PhD Writers",
    description: "Our team consists of qualified academics with advanced degrees from top universities worldwide.",
    highlight: "500+ Expert Writers",
    color: "text-blue-600"
  },
  {
    icon: Clock,
    title: "24/7 Customer Support",
    description: "Round-the-clock assistance from our dedicated support team, ready to help whenever you need us.",
    highlight: "Always Available",
    color: "text-green-600"
  },
  {
    icon: Shield,
    title: "100% Plagiarism-Free",
    description: "Every paper is written from scratch and thoroughly checked with advanced plagiarism detection tools.",
    highlight: "Original Content",
    color: "text-red-600"
  },
  {
    icon: Star,
    title: "Quality Guarantee",
    description: "We guarantee high-quality work that meets your academic standards and requirements.",
    highlight: "98% Satisfaction",
    color: "text-yellow-600"
  },
  {
    icon: Users,
    title: "Personalized Approach",
    description: "Each assignment is tailored to your specific requirements and academic level.",
    highlight: "Custom Solutions",
    color: "text-purple-600"
  },
  {
    icon: FileCheck,
    title: "On-Time Delivery",
    description: "We respect deadlines and ensure your work is delivered on time, every time.",
    highlight: "99% On-Time Rate",
    color: "text-indigo-600"
  },
  {
    icon: Headphones,
    title: "Direct Communication",
    description: "Communicate directly with your assigned writer throughout the writing process.",
    highlight: "Writer Chat",
    color: "text-pink-600"
  },
  {
    icon: Award,
    title: "Academic Excellence",
    description: "Our writers are experts in their fields with proven track records of academic success.",
    highlight: "Top Grades",
    color: "text-orange-600"
  },
  {
    icon: RefreshCw,
    title: "Free Revisions",
    description: "Unlimited revisions until you&apos;re completely satisfied with your paper.",
    highlight: "Unlimited Edits",
    color: "text-teal-600"
  },
  {
    icon: Lock,
    title: "Complete Privacy",
    description: "Your personal information and academic work are kept strictly confidential.",
    highlight: "100% Secure",
    color: "text-gray-600"
  },
  {
    icon: Zap,
    title: "Fast Turnaround",
    description: "Quick delivery options available for urgent assignments without compromising quality.",
    highlight: "Rush Orders",
    color: "text-cyan-600"
  },
  {
    icon: Heart,
    title: "Student-Focused",
    description: "Everything we do is designed with student success and satisfaction in mind.",
    highlight: "Your Success",
    color: "text-rose-600"
  }
];

export function WhyUsFeatures() {
  return (
    <div className="container mx-auto px-4">
      <div className="max-w-6xl mx-auto">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            What Makes Us Different
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Discover the key features that set us apart from other academic writing services
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Card className="h-full hover:shadow-lg transition-all duration-300 border-2 hover:border-primary/20 group">
                <CardContent className="p-6">
                  {/* Icon and Badge */}
                  <div className="flex items-start justify-between mb-4">
                    <div className={`p-3 rounded-lg bg-muted/50 group-hover:bg-primary/10 transition-colors`}>
                      <feature.icon className={`w-6 h-6 ${feature.color} group-hover:text-primary transition-colors`} />
                    </div>
                    <Badge 
                      variant="secondary" 
                      className="text-xs bg-primary/10 text-primary border-primary/20"
                    >
                      {feature.highlight}
                    </Badge>
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-semibold mb-3 group-hover:text-primary transition-colors">
                    {feature.title}
                  </h3>
                  <p className="text-muted-foreground leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}
