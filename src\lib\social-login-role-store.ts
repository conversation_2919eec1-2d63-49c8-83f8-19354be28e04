// Temporary store for social login intended roles
// This is a simple in-memory store for development
// In production, you might want to use Redis or a database

import { UserRole } from "@prisma/client";

interface SocialLoginSession {
  email: string;
  intendedRole: UserRole;
  timestamp: number;
}

// In-memory store (for development)
const socialLoginStore = new Map<string, SocialLoginSession>();

// Clean up old entries (older than 10 minutes)
const CLEANUP_INTERVAL = 10 * 60 * 1000; // 10 minutes
const MAX_AGE = 10 * 60 * 1000; // 10 minutes

setInterval(() => {
  const now = Date.now();
  for (const [key, session] of socialLoginStore.entries()) {
    if (now - session.timestamp > MAX_AGE) {
      socialLoginStore.delete(key);
    }
  }
}, CLEANUP_INTERVAL);

export function storeSocialLoginRole(email: string, intendedRole: UserRole): void {
  socialLoginStore.set(email, {
    email,
    intendedRole,
    timestamp: Date.now(),
  });
}

export function getSocialLoginRole(email: string): UserRole | null {
  const session = socialLoginStore.get(email);
  if (!session) {
    return null;
  }

  // Check if session is still valid
  if (Date.now() - session.timestamp > MAX_AGE) {
    socialLoginStore.delete(email);
    return null;
  }

  return session.intendedRole;
}

export function clearSocialLoginRole(email: string): void {
  socialLoginStore.delete(email);
}

export function getAllStoredRoles(): Record<string, UserRole> {
  const roles: Record<string, UserRole> = {};
  for (const [key, session] of socialLoginStore.entries()) {
    // Check if session is still valid
    if (Date.now() - session.timestamp <= MAX_AGE) {
      roles[key] = session.intendedRole;
    }
  }
  return roles;
}
