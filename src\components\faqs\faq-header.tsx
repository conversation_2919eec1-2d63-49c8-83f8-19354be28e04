"use client";

import { useEffect, useState } from "react";
import { HelpCircle, MessageCircle, Mail } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";

interface CompanyInfo {
  companyName: string;
  supportEmail: string;
}

interface FAQHeaderProps {
  totalCount: number;
}

export function FAQHeader({ totalCount }: FAQHeaderProps) {
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo | null>(null);

  useEffect(() => {
    fetchCompanyInfo();
  }, []);

  const fetchCompanyInfo = async () => {
    try {
      const response = await fetch("/api/company-info");
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setCompanyInfo(result.data);
        }
      }
    } catch (error) {
      console.error("Error fetching company info:", error);
    }
  };

  const companyName = companyInfo?.companyName || "Academic Writing Service";
  const supportEmail = companyInfo?.supportEmail || "<EMAIL>";

  return (
    <div className="text-center space-y-6">
      <div className="space-y-4">
        <div className="flex justify-center">
          <div className="p-4 bg-primary/10 rounded-full">
            <HelpCircle className="h-12 w-12 text-primary" />
          </div>
        </div>
        
        <div className="space-y-2">
          <h1 className="text-4xl md:text-5xl font-bold text-foreground">
            Frequently Asked Questions
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Find answers to common questions about {companyName}&apos;s academic writing services
          </p>
        </div>
        
        <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
          <span className="flex items-center gap-1">
            <MessageCircle className="h-4 w-4" />
            {totalCount} Questions Answered
          </span>
        </div>
      </div>

      <div className="bg-muted/50 rounded-lg p-6 max-w-4xl mx-auto">
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-foreground">
            Can&apos;t find what you&apos;re looking for?
          </h2>
          <p className="text-muted-foreground">
            Our support team is here to help you with any questions not covered in our FAQ.
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button asChild variant="default">
              <Link href="/contact-us">
                <MessageCircle className="h-4 w-4 mr-2" />
                Contact Support
              </Link>
            </Button>
            <Button asChild variant="outline">
              <Link href={`mailto:${supportEmail}`}>
                <Mail className="h-4 w-4 mr-2" />
                Email Us
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
