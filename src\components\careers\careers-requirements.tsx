"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, GraduationCap } from "lucide-react";

const requirements = [
  "Bachelor's degree or higher in any field",
  "Excellent command of English language",
  "Strong research and writing skills",
  "Reliable internet connection",
  "Ability to meet deadlines consistently",
  "Professional communication skills"
];

export function CareersRequirements() {
  return (
    <div className="container mx-auto px-4">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <Badge 
            variant="secondary" 
            className="px-4 py-2 text-sm font-medium bg-primary/10 text-primary border-primary/20 mb-4"
          >
            <GraduationCap className="w-4 h-4 mr-2" />
            Requirements
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            What We&apos;re Looking For
          </h2>
          <p className="text-lg text-muted-foreground">
            Join our team if you meet these basic requirements
          </p>
        </motion.div>

        <Card className="hover:shadow-lg transition-all duration-300">
          <CardContent className="p-8">
            <div className="grid md:grid-cols-2 gap-6">
              {requirements.map((requirement, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="flex items-center"
                >
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-muted-foreground">{requirement}</span>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
