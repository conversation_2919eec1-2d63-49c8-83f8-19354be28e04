"use client";

import { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { useSession } from "next-auth/react";
import { ArrowRight, CheckCircle, Clock, Users, Award } from "lucide-react";

import { OrderForm } from "./order-form";
import { PricingCalculator } from "./pricing-calculator";
import { HowItWorks } from "./how-it-works";
import { ProgressSteps } from "./progress-steps";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useOrderSessionStorage } from "@/lib/order-session-storage";
import { type CreateOrderForm } from "@/types/order";

export function CreateOrderPage() {
  const { data: session, status } = useSession();
  const { hasOrder, getOrderSummary, getRemainingTime } = useOrderSessionStorage();

  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<CreateOrderForm>>({});
  const [calculatedPrice, setCalculatedPrice] = useState(0);
  const [showPendingOrder, setShowPendingOrder] = useState(false);

  // Check for pending order on mount and update step based on auth status
  useEffect(() => {
    if (hasOrder()) {
      setShowPendingOrder(true);
    }

    // Update current step based on authentication status
    if (status === "loading") {
      setCurrentStep(1); // Loading state
    } else if (session?.user) {
      setCurrentStep(2); // Authenticated user - skip to order details
    } else {
      setCurrentStep(1); // Unauthenticated - start from beginning
    }
  }, [hasOrder, session, status]);

  const handleFormDataChange = useCallback((data: Partial<CreateOrderForm>) => {
    setFormData(data);

    // Update step based on form completion
    const requiredFields = ['title', 'description', 'assignmentType', 'subject'];
    const completedFields = requiredFields.filter(field => data[field as keyof CreateOrderForm]);

    if (completedFields.length === requiredFields.length && data.pageCount && data.estTime) {
      setCurrentStep(3); // Form mostly complete
    } else if (completedFields.length >= 2) {
      setCurrentStep(2); // Partially complete
    }
  }, []);

  const handlePriceChange = useCallback((price: number) => {
    setCalculatedPrice(price);
  }, []);

  const orderSummary = getOrderSummary();
  const remainingTime = getRemainingTime();

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Hero Section */}
      <section className="relative py-16 overflow-hidden">
        <div className="absolute inset-0 bg-grid-pattern opacity-5" />
        <div className="container mx-auto px-4 relative">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto mb-12"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-primary via-primary to-primary/70 bg-clip-text text-transparent">
              Create Your Order
            </h1>
            <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
              Get professional academic writing help from qualified experts.
              Fill out our detailed form and receive high-quality work tailored to your requirements.
            </p>

            {/* Dynamic Price Display */}
            {calculatedPrice > 0 && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
                className="mb-6"
              >
                <div className="inline-flex items-center gap-2 bg-primary/10 border border-primary/20 rounded-full px-6 py-3">
                  <span className="text-sm font-medium text-muted-foreground">Current Price:</span>
                  <span className="text-lg font-bold text-primary">${calculatedPrice.toFixed(2)}</span>
                </div>
              </motion.div>
            )}
            
            {/* Trust Indicators with Auth Status */}
            <div className="flex flex-wrap justify-center gap-6 mb-8">
              <div className="flex items-center gap-2 text-sm">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <span>Expert Writers</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Clock className="w-5 h-5 text-blue-500" />
                <span>On-Time Delivery</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Users className="w-5 h-5 text-purple-500" />
                <span>24/7 Support</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Award className="w-5 h-5 text-orange-500" />
                <span>Quality Guaranteed</span>
              </div>

              {/* Authentication Status Indicator */}
              {status !== "loading" && (
                <div className="flex items-center gap-2 text-sm">
                  {session?.user ? (
                    <>
                      <CheckCircle className="w-5 h-5 text-green-500" />
                      <span className="text-green-600">Signed In</span>
                    </>
                  ) : (
                    <>
                      <Users className="w-5 h-5 text-blue-500" />
                      <span className="text-blue-600">Guest Order</span>
                    </>
                  )}
                </div>
              )}
            </div>
          </motion.div>

          {/* Progress Steps */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <ProgressSteps currentStep={currentStep} className="mb-12" />
          </motion.div>
        </div>
      </section>

      {/* Pending Order Alert */}
      {showPendingOrder && orderSummary && (
        <div className="container mx-auto px-4 mb-8">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Alert className="border-orange-200 bg-orange-50 dark:bg-orange-950/20">
              <Clock className="h-4 w-4 text-orange-600" />
              <AlertDescription className="flex items-center justify-between">
                <div>
                  <strong>Pending Order Found:</strong> &ldquo;{orderSummary.title}&rdquo;
                  ({orderSummary.pageCount} pages, ${orderSummary.price.toFixed(2)})
                  {remainingTime > 0 && (
                    <span className="text-sm text-muted-foreground ml-2">
                      • Expires in {remainingTime} minutes
                    </span>
                  )}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowPendingOrder(false)}
                  className="ml-4"
                >
                  Continue with new order
                </Button>
              </AlertDescription>
            </Alert>
          </motion.div>
        </div>
      )}

      {/* Main Content */}
      <section className="pb-16">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid lg:grid-cols-3 gap-8">
              {/* Order Form */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="lg:col-span-2"
              >
                <OrderForm
                  onFormDataChange={handleFormDataChange}
                  onPriceChange={handlePriceChange}
                />
              </motion.div>

              {/* Sidebar */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="space-y-6"
              >
                {/* Pricing Calculator */}
                <PricingCalculator formData={formData} />

                {/* Progress Indicator */}
                <Card className="bg-gradient-to-r from-primary/5 to-primary/10 border-primary/20">
                  <CardContent className="pt-6">
                    <h3 className="font-semibold mb-3 flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-primary animate-pulse" />
                      Order Progress
                    </h3>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Completion</span>
                        <span className="font-medium">{Math.round((currentStep / 4) * 100)}%</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <motion.div
                          className="bg-primary h-2 rounded-full"
                          initial={{ width: "0%" }}
                          animate={{ width: `${(currentStep / 4) * 100}%` }}
                          transition={{ duration: 0.5 }}
                        />
                      </div>
                      <p className="text-xs text-muted-foreground mt-2">
                        {currentStep === 1 && "Getting started..."}
                        {currentStep === 2 && "Making progress..."}
                        {currentStep === 3 && "Almost there!"}
                        {currentStep === 4 && "Ready to submit!"}
                      </p>
                    </div>
                  </CardContent>
                </Card>

                {/* Quick Stats with Price Summary */}
                <Card>
                  <CardContent className="pt-6">
                    <h3 className="font-semibold mb-4">Why Choose Us?</h3>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <Badge variant="secondary" className="w-8 h-8 rounded-full flex items-center justify-center p-0">
                          ✓
                        </Badge>
                        <span className="text-sm">Plagiarism-free content</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Badge variant="secondary" className="w-8 h-8 rounded-full flex items-center justify-center p-0">
                          ✓
                        </Badge>
                        <span className="text-sm">Unlimited revisions</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Badge variant="secondary" className="w-8 h-8 rounded-full flex items-center justify-center p-0">
                          ✓
                        </Badge>
                        <span className="text-sm">Money-back guarantee</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Badge variant="secondary" className="w-8 h-8 rounded-full flex items-center justify-center p-0">
                          ✓
                        </Badge>
                        <span className="text-sm">Confidential & secure</span>
                      </div>
                    </div>

                    {/* Price Summary */}
                    {calculatedPrice > 0 && (
                      <div className="mt-6 pt-4 border-t">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Current Total:</span>
                          <span className="text-lg font-bold text-primary">${calculatedPrice.toFixed(2)}</span>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          Price updates as you fill the form
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Contact Support */}
                <Card className="bg-primary/5 border-primary/20">
                  <CardContent className="pt-6 text-center">
                    <h3 className="font-semibold mb-2">Need Help?</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Our support team is available 24/7 to assist you with your order.
                    </p>
                    <Button variant="outline" size="sm" className="w-full">
                      Contact Support
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <motion.div
        initial={{ opacity: 0, y: 40 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
      >
        <HowItWorks className="bg-muted/30" />
      </motion.div>
    </div>
  );
}
