import { NextRequest } from 'next/server';
import prisma from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { apiError, apiSuccess } from '@/lib/api-utils';

export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin role
    const session = await getServerSession(authConfig);
    if (!session?.user) {
      return apiError('Unauthorized', 401);
    }

    if (session.user.role !== 'ADMIN') {
      return apiError('Admin access required', 403);
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';

    const skip = (page - 1) * limit;

    // Build search conditions
    const searchConditions = search
      ? {
          OR: [
            { taskId: { contains: search, mode: 'insensitive' as const } },
            { title: { contains: search, mode: 'insensitive' as const } },
            { client: { name: { contains: search, mode: 'insensitive' as const } } },
            { assignedWriter: { name: { contains: search, mode: 'insensitive' as const } } }
          ]
        }
      : {};

    // Get assignments with file attachments
    const assignments = await prisma.assignment.findMany({
      where: {
        ...searchConditions,
        fileAttachments: {
          some: {} // Only assignments that have file attachments
        }
      },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        assignedWriter: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        fileAttachments: {
          orderBy: { uploadedAt: 'desc' }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit
    });

    // Get total count for pagination
    const totalCount = await prisma.assignment.count({
      where: {
        ...searchConditions,
        fileAttachments: {
          some: {}
        }
      }
    });

    // Transform data for frontend
    const transformedData = assignments.map(assignment => ({
      id: assignment.id,
      taskId: assignment.taskId,
      title: assignment.title,
      status: assignment.status,
      createdAt: assignment.createdAt.toISOString(),
      client: assignment.client,
      assignedWriter: assignment.assignedWriter,
      fileCount: assignment.fileAttachments.length,
      files: assignment.fileAttachments.map(file => ({
        id: file.id,
        fileName: file.fileName,
        originalName: file.originalName,
        fileUrl: file.fileUrl,
        fileSize: file.fileSize,
        fileType: file.fileType,
        uploadedAt: file.uploadedAt.toISOString()
      }))
    }));

    return apiSuccess({
      assignments: transformedData,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limit)
      }
    }, 'Data library retrieved successfully');

  } catch (error) {
    console.error('Data library error:', error);
    return apiError('Internal server error', 500);
  }
}
