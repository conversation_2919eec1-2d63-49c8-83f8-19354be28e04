"use client";

import { motion } from "framer-motion";
import { WhyUsHero } from "./why-us-hero";
import { WhyUsFeatures } from "./why-us-features";
import { WhyUsStats } from "./why-us-stats";
import { WhyUsTestimonials } from "./why-us-testimonials";
import { WhyUsGuarantees } from "./why-us-guarantees";
import { WhyUsCTA } from "./why-us-cta";

export function WhyUsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Hero Section */}
      <WhyUsHero />

      {/* Features Section */}
      <motion.section
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="py-16"
      >
        <WhyUsFeatures />
      </motion.section>

      {/* Statistics Section */}
      <motion.section
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="py-16 bg-muted/30"
      >
        <WhyUsStats />
      </motion.section>

      {/* Guarantees Section */}
      <motion.section
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="py-16"
      >
        <WhyUsGuarantees />
      </motion.section>

      {/* Testimonials Section */}
      <motion.section
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="py-16 bg-muted/30"
      >
        <WhyUsTestimonials />
      </motion.section>

      {/* Call to Action Section */}
      <motion.section
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="py-16"
      >
        <WhyUsCTA />
      </motion.section>
    </div>
  );
}
