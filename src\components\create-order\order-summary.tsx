"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { But<PERSON> } from "@/components/ui/button";
import {
  FileText,
  Clock,
  GraduationCap,
  DollarSign,
  Calendar,
  BookOpen,
  Edit3
} from "lucide-react";
import { format } from "date-fns";
import { type CreateOrderForm, priorityOptions, academicLevelOptions } from "@/types/order";

interface OrderSummaryProps {
  formData: Partial<CreateOrderForm>;
  calculatedPrice: number;
  uploadedFilesCount: number;
  onEdit?: () => void;
  className?: string;
}

export function OrderSummary({ 
  formData, 
  calculatedPrice, 
  uploadedFilesCount, 
  onEdit,
  className = "" 
}: OrderSummaryProps) {
  const priorityInfo = priorityOptions.find(p => p.value === formData.priority);
  const academicLevelInfo = academicLevelOptions.find(a => a.value === formData.academicLevel);

  const summaryItems = [
    {
      icon: FileText,
      label: "Assignment Type",
      value: formData.assignmentType?.replace(/_/g, ' ') || "Not specified",
    },
    {
      icon: BookOpen,
      label: "Subject",
      value: formData.subject || "Not specified",
    },
    {
      icon: FileText,
      label: "Pages",
      value: formData.pageCount ? `${formData.pageCount} pages` : "Not specified",
    },
    {
      icon: GraduationCap,
      label: "Academic Level",
      value: academicLevelInfo?.label || "Not specified",
    },
    {
      icon: Clock,
      label: "Priority",
      value: priorityInfo?.label || "Not specified",
      badge: priorityInfo?.color,
    },
    {
      icon: Calendar,
      label: "Deadline",
      value: formData.estTime ? format(formData.estTime, "PPP") : "Not specified",
    },
  ];

  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5 text-primary" />
            Order Summary
          </CardTitle>
          {onEdit && (
            <Button variant="ghost" size="sm" onClick={onEdit}>
              <Edit3 className="w-4 h-4" />
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Title */}
        {formData.title && (
          <div>
            <h3 className="font-semibold text-lg mb-2 line-clamp-2">
              {formData.title}
            </h3>
            {formData.description && (
              <p className="text-sm text-muted-foreground line-clamp-3">
                {formData.description}
              </p>
            )}
          </div>
        )}

        <Separator />

        {/* Summary Items */}
        <div className="space-y-3">
          {summaryItems.map((item, index) => {
            const IconComponent = item.icon;
            return (
              <motion.div
                key={item.label}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className="flex items-center justify-between"
              >
                <div className="flex items-center gap-2">
                  <IconComponent className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">{item.label}:</span>
                </div>
                <div className="flex items-center gap-2">
                  {item.badge && (
                    <Badge className={item.badge} variant="secondary">
                      {item.value}
                    </Badge>
                  )}
                  {!item.badge && (
                    <span className="text-sm font-medium text-right max-w-[150px] truncate">
                      {item.value}
                    </span>
                  )}
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Additional Details */}
        {(formData.numSources || formData.spacing || formData.formatStyle) && (
          <>
            <Separator />
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-muted-foreground">Additional Details</h4>
              <div className="grid grid-cols-2 gap-2 text-xs">
                {formData.numSources !== undefined && (
                  <div>Sources: {formData.numSources}</div>
                )}
                {formData.spacing && (
                  <div>Spacing: {formData.spacing.replace('_', ' ')}</div>
                )}
                {formData.formatStyle && (
                  <div>Format: {formData.formatStyle}</div>
                )}
                {formData.languageStyle && (
                  <div>Language: {formData.languageStyle.replace(/_/g, ' ')}</div>
                )}
              </div>
            </div>
          </>
        )}

        {/* Files */}
        {uploadedFilesCount > 0 && (
          <>
            <Separator />
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Uploaded Files:</span>
              <Badge variant="secondary">
                {uploadedFilesCount} file{uploadedFilesCount > 1 ? 's' : ''}
              </Badge>
            </div>
          </>
        )}

        {/* Guidelines Preview */}
        {formData.guidelines && (
          <>
            <Separator />
            <div>
              <h4 className="text-sm font-medium text-muted-foreground mb-2">Guidelines</h4>
              <p className="text-xs text-muted-foreground line-clamp-3 bg-muted/50 p-2 rounded">
                {formData.guidelines}
              </p>
            </div>
          </>
        )}

        <Separator />

        {/* Total Price */}
        <div className="flex items-center justify-between text-lg font-semibold">
          <div className="flex items-center gap-2">
            <DollarSign className="w-5 h-5 text-primary" />
            <span>Total Price:</span>
          </div>
          <span className="text-primary">${calculatedPrice.toFixed(2)}</span>
        </div>

        {/* Delivery Estimate */}
        {priorityInfo && (
          <div className="p-3 bg-primary/5 rounded-lg border border-primary/20">
            <div className="flex items-center gap-2 text-sm">
              <Clock className="w-4 h-4 text-primary" />
              <span className="font-medium">Estimated Delivery:</span>
              <span className="text-primary">{priorityInfo.description}</span>
            </div>
          </div>
        )}

        {/* Completion Status */}
        <div className="text-center">
          <div className="text-xs text-muted-foreground">
            {formData.title && formData.description && formData.pageCount && formData.estTime ? (
              <span className="text-green-600 font-medium">✓ Ready to submit</span>
            ) : (
              <span>Complete all required fields to proceed</span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
