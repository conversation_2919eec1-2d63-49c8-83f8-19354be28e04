// src/app/api/users/[id]/route.ts
import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import {
  apiSuccess,
  apiError,
  parseRequestBody,
  getCurrentUserId,
  getCurrentUserRole,
  safeJsonDate,
} from "@/lib/api-utils";
import { userUpdateSchema } from "@/lib/validations";
import { UserResponse, UserUpdateData } from "@/types/api";
import { UserRole } from "@prisma/client";

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const currentUserId = await getCurrentUserId();
    const currentUserRole = await getCurrentUserRole();
    
    if (!currentUserId) {
      return apiError("Authentication required", 401);
    }

    const { id } = await params;

    // Check permissions: users can only access their own data, admins can access any user
    if (currentUserRole !== UserRole.ADMIN && currentUserId !== id) {
      return apiError("Forbidden: You can only access your own profile", 403);
    }

    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        accountId: true,
        name: true,
        email: true,
        phone: true,
        role: true,
        isApproved: true,
        emailVerified: true,
        professionalSummary: true,
        experience: true,
        competencies: true,
        educationLevel: true,
        rating: true,
        createdAt: true,
        updatedAt: true,
        image: true,
      },
    });

    if (!user) {
      return apiError("User not found", 404);
    }

    return apiSuccess(safeJsonDate(user) as UserResponse);
  } catch (error) {
    console.error("Error fetching user:", error);
    return apiError("Failed to fetch user", 500);
  }
}

export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const currentUserId = await getCurrentUserId();
    const currentUserRole = await getCurrentUserRole();
    
    if (!currentUserId) {
      return apiError("Authentication required", 401);
    }

    const { id } = await params;

    // Check permissions: users can only update their own data, admins can update any user
    if (currentUserRole !== UserRole.ADMIN && currentUserId !== id) {
      return apiError("Forbidden: You can only update your own profile", 403);
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id },
    });

    if (!existingUser) {
      return apiError("User not found", 404);
    }

    // Parse and validate request body
    const result = await parseRequestBody(req, userUpdateSchema);
    if ("success" in result && result.success === false) {
      return apiError(result.message, 400, result.errors);
    }

    const updateData = result as UserUpdateData;

    const updatedUser = await prisma.user.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        accountId: true,
        name: true,
        email: true,
        phone: true,
        role: true,
        isApproved: true,
        emailVerified: true,
        professionalSummary: true,
        experience: true,
        competencies: true,
        educationLevel: true,
        rating: true,
        createdAt: true,
        updatedAt: true,
        image: true,
      },
    });

    return apiSuccess(
      safeJsonDate(updatedUser) as UserResponse,
      "User updated successfully"
    );
  } catch (error) {
    console.error("Error updating user:", error);
    return apiError("Failed to update user", 500);
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const currentUserRole = await getCurrentUserRole();
    
    // Only admins can delete users
    if (currentUserRole !== UserRole.ADMIN) {
      return apiError("Forbidden: Only admins can delete users", 403);
    }

    const { id } = await params;

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id },
    });

    if (!existingUser) {
      return apiError("User not found", 404);
    }

    await prisma.user.delete({
      where: { id },
    });

    return apiSuccess(null, "User deleted successfully");
  } catch (error) {
    console.error("Error deleting user:", error);
    return apiError("Failed to delete user", 500);
  }
}
