// src/components/dashboard/client/dashboard-large-card.tsx
"use client";

import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import {
  User,
  Mail,
  Phone,
  Calendar,
  Edit3,
  Save,
  X,
  CheckCircle,
  Clock,
  Loader2,
} from "lucide-react";
import {
  useClientDashboard,
  ClientDashboardUpdateData,
} from "@/hooks/use-client-dashboard";

interface ClientProfileProps {
  className?: string;
  avatarUrl?: string;
  joinDate?: string;
}

export default function ClientProfile({
  className = "",
  avatarUrl = "/avatars/shadcn.jpg",
}: ClientProfileProps) {
  const [editMode, setEditMode] = useState({
    profile: false,
  });

  const {
    clientData,
    loading,
    error,
    updateClient,
    isUpdating,
  } = useClientDashboard();

  const [localData, setLocalData] = useState({
    name: "",
    email: "",
    phone: "",
  });

  // Update local data when clientData changes
  useEffect(() => {
    if (clientData) {
      setLocalData({
        name: clientData.name || "",
        email: clientData.email || "",
        phone: clientData.phone || "",
      });
    }
  }, [clientData]);

  const handleSaveProfile = async () => {
    const updateData: ClientDashboardUpdateData = {
      name: localData.name || null,
      phone: localData.phone || null,
    };

    const success = await updateClient(updateData);
    if (success) {
      setEditMode({ profile: false });
    }
  };

  const handleCancelEdit = () => {
    if (clientData) {
      setLocalData({
        name: clientData.name || "",
        email: clientData.email || "",
        phone: clientData.phone || "",
      });
    }
    setEditMode({ profile: false });
  };

  if (loading) {
    return (
      <Card className={`h-full ${className}`}>
        <CardContent className="flex items-center justify-center h-full">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">Loading profile...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={`h-full ${className}`}>
        <CardContent className="flex items-center justify-center h-full">
          <div className="text-center">
            <p className="text-sm text-destructive">Error loading profile</p>
            <p className="text-xs text-muted-foreground mt-1">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!clientData) {
    return (
      <Card className={`h-full ${className}`}>
        <CardContent className="flex items-center justify-center h-full">
          <p className="text-sm text-muted-foreground">No profile data available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`h-full overflow-hidden ${className}`}>
      <CardHeader className="pb-4">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
          <CardTitle className="text-lg sm:text-xl font-bold">Client Profile</CardTitle>
          <div className="flex items-center gap-2">
            {clientData.isEmailVerified ? (
              <Badge variant="default" className="bg-green-500 hover:bg-green-600">
                <CheckCircle className="h-3 w-3 mr-1" />
                Verified
              </Badge>
            ) : (
              <Badge variant="secondary">
                <Clock className="h-3 w-3 mr-1" />
                Pending
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Profile Section */}
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
            <h3 className="text-base sm:text-lg font-semibold">Personal Information</h3>
            {!editMode.profile ? (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setEditMode({ profile: true })}
                className="self-start sm:self-auto"
              >
                <Edit3 className="h-4 w-4 mr-2" />
                Edit
              </Button>
            ) : (
              <div className="flex gap-2 self-start sm:self-auto">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancelEdit}
                  disabled={isUpdating}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={handleSaveProfile}
                  disabled={isUpdating}
                >
                  {isUpdating ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  Save
                </Button>
              </div>
            )}
          </div>

          <div className="flex flex-col sm:flex-row items-start gap-4">
            <Avatar className="h-16 w-16 mx-auto sm:mx-0">
              <AvatarImage src={clientData.image || avatarUrl} alt="Profile" />
              <AvatarFallback>
                {clientData.name
                  ? clientData.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")
                      .toUpperCase()
                  : "CL"}
              </AvatarFallback>
            </Avatar>

            <div className="flex-1 space-y-3 w-full">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Full Name
                  </label>
                  {editMode.profile ? (
                    <Input
                      value={localData.name}
                      onChange={(e) =>
                        setLocalData({ ...localData, name: e.target.value })
                      }
                      placeholder="Enter your full name"
                    />
                  ) : (
                    <p className="text-sm text-muted-foreground">
                      {clientData.name || "Not provided"}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    Email Address
                  </label>
                  <p className="text-sm text-muted-foreground">
                    {clientData.email}
                  </p>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium flex items-center gap-2">
                    <Phone className="h-4 w-4" />
                    Phone Number
                  </label>
                  {editMode.profile ? (
                    <Input
                      value={localData.phone}
                      onChange={(e) =>
                        setLocalData({ ...localData, phone: e.target.value })
                      }
                      placeholder="Enter your phone number"
                    />
                  ) : (
                    <p className="text-sm text-muted-foreground">
                      {clientData.phone || "Not provided"}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Member Since
                  </label>
                  <p className="text-sm text-muted-foreground">
                    {new Date(clientData.createdAt).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Account Information */}
        <div className="space-y-4">
          <h3 className="text-base sm:text-lg font-semibold">Account Information</h3>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Account ID</label>
              <p className="text-sm text-muted-foreground font-mono">
                {clientData.accountId || "Not assigned"}
              </p>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Last Updated</label>
              <p className="text-sm text-muted-foreground">
                {new Date(clientData.updatedAt).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
