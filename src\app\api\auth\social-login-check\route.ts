import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { UserRole } from "@prisma/client";

interface SocialLoginCheckRequest {
  email: string;
  intendedRole: UserRole;
  provider: string;
}

export async function POST(req: NextRequest) {
  try {
    const { email, intendedRole, provider }: SocialLoginCheckRequest = await req.json();

    if (!email || !intendedRole || !provider) {
      return NextResponse.json(
        { error: "Email, intended role, and provider are required" },
        { status: 400 }
      );
    }

    // Find the user by email
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        role: true,
        email: true,
        name: true,
      },
    });

    if (!user) {
      // User doesn't exist - they can proceed with social login
      // The role will be set based on the intended role from the login page
      return NextResponse.json({
        canProceed: true,
        isNewUser: true,
        message: "New user can proceed with social login",
      });
    }

    // User exists - check if their role matches the intended role
    if (user.role !== intendedRole) {
      const correctLoginPage = getCorrectLoginPage(user.role);
      const roleName = getRoleName(user.role);
      
      return NextResponse.json({
        canProceed: false,
        isNewUser: false,
        existingRole: user.role,
        correctLoginPage,
        message: `Please login through the ${roleName} dashboard at ${correctLoginPage}`,
      });
    }

    // User exists and has the correct role - they can proceed
    return NextResponse.json({
      canProceed: true,
      isNewUser: false,
      existingRole: user.role,
      message: "Existing user can proceed with social login",
    });
  } catch (error) {
    console.error("Error checking social login:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

function getCorrectLoginPage(role: UserRole): string {
  switch (role) {
    case "ADMIN":
      return "/login/admin";
    case "WRITER":
      return "/login/writer";
    case "CLIENT":
      return "/login/client";
    default:
      return "/login/client";
  }
}

function getRoleName(role: UserRole): string {
  switch (role) {
    case "ADMIN":
      return "Admin";
    case "WRITER":
      return "Writer";
    case "CLIENT":
      return "Client";
    default:
      return "Client";
  }
}
