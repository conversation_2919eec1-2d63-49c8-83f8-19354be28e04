"use client";

import { useEffect, useState, Suspense } from "react";
import { useSearch<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON>Circle, XCircle, Loader2, Mail } from "lucide-react";
import Link from "next/link";

interface VerificationState {
  status: 'loading' | 'success' | 'error';
  message: string;
  redirectPath?: string;
}

function VerifyEmailContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [verificationState, setVerificationState] = useState<VerificationState>({
    status: 'loading',
    message: 'Verifying your email address...'
  });

  useEffect(() => {
    const token = searchParams.get('token');
    
    if (!token) {
      setVerificationState({
        status: 'error',
        message: 'Invalid verification link. No token provided.'
      });
      return;
    }

    // Call the verification API
    const verifyEmail = async () => {
      try {
        const response = await fetch(`/api/auth/verify-email?token=${token}`, {
          method: 'GET',
        });

        if (response.ok) {
          // Check if it's a redirect response
          if (response.redirected) {
            const redirectUrl = new URL(response.url);
            const pathname = redirectUrl.pathname;
            const searchParams = redirectUrl.searchParams;
            
            if (searchParams.get('success') === 'EmailVerified') {
              setVerificationState({
                status: 'success',
                message: 'Email verified successfully! You can now log in to your account.',
                redirectPath: pathname
              });
              
              // Auto-redirect after 3 seconds
              setTimeout(() => {
                router.push(pathname);
              }, 3000);
            } else {
              setVerificationState({
                status: 'success',
                message: 'Email verified successfully! Redirecting to your dashboard...',
                redirectPath: pathname
              });
              
              // Auto-redirect after 2 seconds
              setTimeout(() => {
                router.push(pathname);
              }, 2000);
            }
          } else {
            setVerificationState({
              status: 'success',
              message: 'Email verified successfully!'
            });
          }
        } else {
          
          const errorData = await response.text();
          console.error('Email verification failed:', errorData);
          setVerificationState({
            status: 'error',
            message: 'Verification failed. The link may be invalid or expired.'
          });
        }
      } catch (error) {
        console.error('Verification error:', error);
        setVerificationState({
          status: 'error',
          message: 'An error occurred during verification. Please try again.'
        });
      }
    };

    verifyEmail();
  }, [searchParams, router]);

  const getIcon = () => {
    switch (verificationState.status) {
      case 'loading':
        return <Loader2 className="h-16 w-16 text-primary animate-spin" />;
      case 'success':
        return <CheckCircle className="h-16 w-16 text-green-500" />;
      case 'error':
        return <XCircle className="h-16 w-16 text-red-500" />;
    }
  };

  const getTitle = () => {
    switch (verificationState.status) {
      case 'loading':
        return 'Verifying Email';
      case 'success':
        return 'Email Verified!';
      case 'error':
        return 'Verification Failed';
    }
  };

  return (
    <div className="container flex h-screen w-full items-center justify-center px-4 py-8">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {getIcon()}
          </div>
          <CardTitle className="text-2xl font-bold">
            {getTitle()}
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-muted-foreground">
            {verificationState.message}
          </p>
          
          {verificationState.status === 'success' && verificationState.redirectPath && (
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                You will be redirected automatically, or click below:
              </p>
              <Button asChild className="w-full">
                <Link href={verificationState.redirectPath}>
                  Continue to Dashboard
                </Link>
              </Button>
            </div>
          )}
          
          {verificationState.status === 'success' && !verificationState.redirectPath && (
            <Button asChild className="w-full">
              <Link href="/login/client">
                Go to Login
              </Link>
            </Button>
          )}
          
          {verificationState.status === 'error' && (
            <div className="space-y-3">
              <Button asChild variant="outline" className="w-full">
                <Link href="/login/client">
                  <Mail className="h-4 w-4 mr-2" />
                  Go to Login
                </Link>
              </Button>
              <p className="text-xs text-muted-foreground">
                Need help? Contact our support team.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export default function VerifyEmailPage() {
  return (
    <Suspense fallback={
      <div className="container flex h-screen w-full items-center justify-center px-4 py-8">
        <Card className="w-full max-w-md">
          <CardContent className="text-center py-8">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading...</p>
          </CardContent>
        </Card>
      </div>
    }>
      <VerifyEmailContent />
    </Suspense>
  );
}
