import { NextRequest } from "next/server";
import { getSession } from "@/lib/auth-utils";
import { notificationService } from "@/lib/notification-service";

export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const userId = session.user.id;

    // Create a readable stream for SSE
    const stream = new ReadableStream({
      start(controller) {
        // Add this connection to the notification service
        notificationService.addSSEConnection(userId, new Response(), controller);

        // Send initial connection confirmation
        const initialData = `data: ${JSON.stringify({
          type: 'connection-established',
          message: 'SSE connection established',
          timestamp: new Date().toISOString(),
        })}\n\n`;
        
        controller.enqueue(new TextEncoder().encode(initialData));

        // Keep connection alive with periodic heartbeat
        const heartbeat = setInterval(() => {
          try {
            const heartbeatData = `data: ${JSON.stringify({
              type: 'heartbeat',
              timestamp: new Date().toISOString(),
            })}\n\n`;
            controller.enqueue(new TextEncoder().encode(heartbeatData));
          } catch (error) {
            console.error('Heartbeat error:', error);
            clearInterval(heartbeat);
            notificationService.removeSSEConnection(userId, controller);
          }
        }, 30000); // Send heartbeat every 30 seconds

        // Clean up on close
        request.signal.addEventListener('abort', () => {
          clearInterval(heartbeat);
          notificationService.removeSSEConnection(userId, controller);
          controller.close();
        });
      },
      cancel() {
        notificationService.removeSSEConnection(userId, new ReadableStreamDefaultController());
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      },
    });
  } catch (error) {
    console.error("SSE connection error:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}
