import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { apiSuccess, apiError, checkPermission, parseRequestBody } from "@/lib/api-utils";
import { faqUpdateSchema } from "@/lib/validations";
import { UserRole } from "@prisma/client";

// GET /api/admin/faqs/[id] - Get single FAQ
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check admin permission
    const permissionError = await checkPermission([UserRole.ADMIN]);
    if (permissionError) return permissionError;

    const { id } = await params;

    if (!id) {
      return apiError("FAQ ID is required", 400);
    }

    const faq = await prisma.fAQ.findUnique({
      where: { id },
    });

    if (!faq) {
      return apiError("FAQ not found", 404);
    }

    return apiSuccess(faq);
  } catch (error) {
    console.error("Error fetching FAQ:", error);
    return apiError("Failed to fetch FAQ", 500);
  }
}

// PUT /api/admin/faqs/[id] - Update FAQ
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check admin permission
    const permissionError = await checkPermission([UserRole.ADMIN]);
    if (permissionError) return permissionError;

    const { id } = await params;

    if (!id) {
      return apiError("FAQ ID is required", 400);
    }

    // Parse and validate request body
    const parseResult = await parseRequestBody(request, faqUpdateSchema);
    if ("message" in parseResult) {
      return apiError(parseResult.message, 400, parseResult.errors);
    }

    const data = parseResult;

    // Check if FAQ exists
    const existingFaq = await prisma.fAQ.findUnique({
      where: { id },
    });

    if (!existingFaq) {
      return apiError("FAQ not found", 404);
    }

    // Update FAQ
    const updatedFaq = await prisma.fAQ.update({
      where: { id },
      data,
    });

    return apiSuccess(updatedFaq, "FAQ updated successfully");
  } catch (error) {
    console.error("Error updating FAQ:", error);
    return apiError("Failed to update FAQ", 500);
  }
}

// DELETE /api/admin/faqs/[id] - Delete FAQ
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check admin permission
    const permissionError = await checkPermission([UserRole.ADMIN]);
    if (permissionError) return permissionError;

    const { id } = await params;

    if (!id) {
      return apiError("FAQ ID is required", 400);
    }

    // Check if FAQ exists
    const existingFaq = await prisma.fAQ.findUnique({
      where: { id },
    });

    if (!existingFaq) {
      return apiError("FAQ not found", 404);
    }

    // Delete FAQ
    await prisma.fAQ.delete({
      where: { id },
    });

    return apiSuccess(null, "FAQ deleted successfully");
  } catch (error) {
    console.error("Error deleting FAQ:", error);
    return apiError("Failed to delete FAQ", 500);
  }
}
