import { Metadata } from "next";
import { getRouteMetadata } from "@/lib/route-metadata";
import { WriterRegistrationForm } from "@/components/writer-registration-form";

export async function generateMetadata(): Promise<Metadata> {
  return await getRouteMetadata("/register/writer");
}

export default function ClientRegistrationPage() {
  return (
    <div className="container flex h-fit w-full items-center justify-center px-4 py-8">
      <WriterRegistrationForm className="w-full max-w-4xl" />
    </div>
  );
}
