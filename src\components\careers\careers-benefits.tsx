"use client";

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  DollarSign, 
  Clock, 
  Home, 
  TrendingUp,
  Users,
  Award,
  Globe,
  Shield,
  Zap,
  Heart,
  BookOpen,
  Star
} from "lucide-react";

const benefits = [
  {
    icon: DollarSign,
    title: "Competitive Compensation",
    description: "Earn $15-50 per page based on complexity and urgency. Top writers earn $3000+ monthly.",
    highlight: "$15-50/page",
    color: "text-green-600",
    bgColor: "bg-green-50 dark:bg-green-950/20"
  },
  {
    icon: Clock,
    title: "Flexible Schedule",
    description: "Work whenever you want, wherever you are. Perfect for students, professionals, and freelancers.",
    highlight: "24/7 Flexibility",
    color: "text-blue-600",
    bgColor: "bg-blue-50 dark:bg-blue-950/20"
  },
  {
    icon: Home,
    title: "100% Remote Work",
    description: "Work from the comfort of your home or anywhere in the world with an internet connection.",
    highlight: "Work From Home",
    color: "text-purple-600",
    bgColor: "bg-purple-50 dark:bg-purple-950/20"
  },
  {
    icon: TrendingUp,
    title: "Career Growth",
    description: "Advance through our writer levels with increased rates and priority access to premium orders.",
    highlight: "Level Up System",
    color: "text-orange-600",
    bgColor: "bg-orange-50 dark:bg-orange-950/20"
  },
  {
    icon: Users,
    title: "Supportive Community",
    description: "Join a community of like-minded writers with forums, mentorship, and collaboration opportunities.",
    highlight: "Writer Network",
    color: "text-pink-600",
    bgColor: "bg-pink-50 dark:bg-pink-950/20"
  },
  {
    icon: Award,
    title: "Recognition Program",
    description: "Get recognized for excellent work with writer of the month awards and performance bonuses.",
    highlight: "Monthly Awards",
    color: "text-yellow-600",
    bgColor: "bg-yellow-50 dark:bg-yellow-950/20"
  },
  {
    icon: Globe,
    title: "Global Reach",
    description: "Work with students from universities worldwide and expand your cultural and academic horizons.",
    highlight: "150+ Countries",
    color: "text-teal-600",
    bgColor: "bg-teal-50 dark:bg-teal-950/20"
  },
  {
    icon: Shield,
    title: "Secure Payments",
    description: "Guaranteed payments through PayPal, bank transfer, or other secure methods. Never worry about getting paid.",
    highlight: "Payment Security",
    color: "text-red-600",
    bgColor: "bg-red-50 dark:bg-red-950/20"
  },
  {
    icon: Zap,
    title: "Instant Notifications",
    description: "Get notified immediately when new orders match your expertise and availability.",
    highlight: "Real-time Alerts",
    color: "text-indigo-600",
    bgColor: "bg-indigo-50 dark:bg-indigo-950/20"
  },
  {
    icon: Heart,
    title: "Make a Difference",
    description: "Help students achieve their academic goals and make a positive impact on their educational journey.",
    highlight: "Student Success",
    color: "text-rose-600",
    bgColor: "bg-rose-50 dark:bg-rose-950/20"
  },
  {
    icon: BookOpen,
    title: "Diverse Projects",
    description: "Work on various academic disciplines and project types to keep your work interesting and challenging.",
    highlight: "50+ Subjects",
    color: "text-cyan-600",
    bgColor: "bg-cyan-50 dark:bg-cyan-950/20"
  },
  {
    icon: Star,
    title: "Quality Focus",
    description: "We prioritize quality over quantity, ensuring you have adequate time to produce excellent work.",
    highlight: "Quality First",
    color: "text-amber-600",
    bgColor: "bg-amber-50 dark:bg-amber-950/20"
  }
];

export function CareersBenefits() {
  return (
    <div className="container mx-auto px-4">
      <div className="max-w-6xl mx-auto">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <Badge 
            variant="secondary" 
            className="px-4 py-2 text-sm font-medium bg-primary/10 text-primary border-primary/20 mb-4"
          >
            <Award className="w-4 h-4 mr-2" />
            Why Join Us
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            Benefits of Being Our Writer
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Discover the advantages of joining our team of expert academic writers and start building your freelance career today
          </p>
        </motion.div>

        {/* Benefits Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {benefits.map((benefit, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Card className="h-full hover:shadow-lg transition-all duration-300 border-2 hover:border-primary/20 group">
                <CardContent className="p-6">
                  {/* Icon and Badge */}
                  <div className="flex items-start justify-between mb-4">
                    <div className={`p-3 rounded-lg ${benefit.bgColor} group-hover:scale-110 transition-transform duration-300`}>
                      <benefit.icon className={`w-6 h-6 ${benefit.color}`} />
                    </div>
                    <Badge 
                      variant="secondary" 
                      className="text-xs bg-primary/10 text-primary border-primary/20"
                    >
                      {benefit.highlight}
                    </Badge>
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-semibold mb-3 group-hover:text-primary transition-colors">
                    {benefit.title}
                  </h3>
                  <p className="text-muted-foreground leading-relaxed">
                    {benefit.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10 rounded-2xl p-8 border border-primary/20">
            <h3 className="text-2xl font-bold mb-4">
              Ready to Start Your Writing Career?
            </h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Join hundreds of successful writers who have built thriving careers with us. Your expertise is valuable, and we&apos;re here to help you monetize it.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Badge variant="secondary" className="px-4 py-2">
                ✓ No Experience Required
              </Badge>
              <Badge variant="secondary" className="px-4 py-2">
                ✓ Free Training Provided
              </Badge>
              <Badge variant="secondary" className="px-4 py-2">
                ✓ Immediate Start
              </Badge>
              <Badge variant="secondary" className="px-4 py-2">
                ✓ Ongoing Support
              </Badge>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
