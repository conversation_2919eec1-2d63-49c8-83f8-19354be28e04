"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  CreditCard,
  Eye,
  Phone,
  CheckCircle,
  Clock,
  Copy,
  User,
  ChevronDown
} from "lucide-react";
import { toast } from "sonner";
import { format } from "date-fns";
import WriterPayPalButton from "@/components/WriterPayPalButtonNew";

interface WriterPayment {
  id: string;
  assignmentId: string;
  writerId: string;
  writerCompensation: number;
  isWriterPaid: boolean;
  writerPaypalEmail?: string;
  writerPaymentDate?: string;
  assignment: {
    id: string;
    title: string;
    taskId: string;
    status: string;
    price: number;
    createdAt: string;
    updatedAt: string;
  };
  writer: {
    id: string;
    name: string | null;
    email: string;
    phone: string | null;
  };
}

interface GroupedWriterPayments {
  writer: {
    id: string;
    name: string | null;
    email: string;
    phone: string | null;
  };
  payments: WriterPayment[];
  totalAmount: number;
  taskCount: number;
}

export default function AdminWriterPaymentsPage() {
  const [pendingPayments, setPendingPayments] = useState<WriterPayment[]>([]);
  const [approvedPayments, setApprovedPayments] = useState<WriterPayment[]>([]);
  const [groupedPendingPayments, setGroupedPendingPayments] = useState<GroupedWriterPayments[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPayment, setSelectedPayment] = useState<WriterPayment | null>(null);
  const [selectedGroupedPayment, setSelectedGroupedPayment] = useState<GroupedWriterPayments | null>(null);
  const [paymentDrawerOpen, setPaymentDrawerOpen] = useState(false);

  const breadcrumbs = [
    { label: "Dashboard", href: "/admin/dashboard" },
    { label: "Writer Payments", isCurrentPage: true },
  ];

  useEffect(() => {
    fetchPayments();
  }, []);

  // Group payments by writer
  const groupPaymentsByWriter = (payments: WriterPayment[]): GroupedWriterPayments[] => {
    const grouped = payments.reduce((acc, payment) => {
      const writerId = payment.writerId;
      if (!acc[writerId]) {
        acc[writerId] = {
          writer: payment.writer,
          payments: [],
          totalAmount: 0,
          taskCount: 0,
        };
      }
      acc[writerId].payments.push(payment);
      acc[writerId].totalAmount += payment.writerCompensation;
      acc[writerId].taskCount += 1;
      return acc;
    }, {} as Record<string, GroupedWriterPayments>);

    return Object.values(grouped).sort((a, b) => b.totalAmount - a.totalAmount);
  };

  const fetchPayments = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/admin/writer-payments");
      if (!response.ok) throw new Error("Failed to fetch payments");

      const data = await response.json();
      if (data.success) {
        const pending = data.data.pending || [];
        const approved = data.data.approved || [];

        setPendingPayments(pending);
        setApprovedPayments(approved);
        setGroupedPendingPayments(groupPaymentsByWriter(pending));
      }
    } catch (error) {
      console.error("Error fetching payments:", error);
      toast.error("Failed to fetch writer payments");
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentSuccess = async (paymentId: string, details: { id: string; status?: string; payerID?: string; paymentID: string }) => {
    try {
      const response = await fetch(`/api/admin/writer-payments/${paymentId}/complete`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          writerPaypalOrderId: details.id,
          writerPaypalPaymentId: details.paymentID,
          paymentMethod: "paypal_payout",
        }),
      });

      if (!response.ok) throw new Error("Failed to complete payment");

      toast.success("Payment completed successfully! 🎉");
      setPaymentDrawerOpen(false);
      setSelectedPayment(null);
      setSelectedGroupedPayment(null);
      await fetchPayments(); // Refresh the data
    } catch (error) {
      console.error("Error completing payment:", error);
      toast.error("Failed to complete payment");
    }
  };

  const handleBulkPaymentSuccess = async (groupedPayment: GroupedWriterPayments, details: { id: string; status?: string; payerID?: string; paymentID: string }) => {
    try {
      // Mark all payments in the group as completed
      const promises = groupedPayment.payments.map(payment =>
        fetch(`/api/admin/writer-payments/${payment.id}/complete`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            writerPaypalOrderId: details.id,
            writerPaypalPaymentId: details.paymentID,
            paymentMethod: "paypal_payout",
            bulkPayment: true,
            totalAmount: groupedPayment.totalAmount,
          }),
        })
      );

      const responses = await Promise.all(promises);
      const failedPayments = responses.filter(response => !response.ok);

      if (failedPayments.length > 0) {
        throw new Error(`Failed to complete ${failedPayments.length} payments`);
      }

      toast.success(`All ${groupedPayment.taskCount} payments completed successfully! 🎉`);
      setPaymentDrawerOpen(false);
      setSelectedGroupedPayment(null);
      await fetchPayments(); // Refresh the data
    } catch (error) {
      console.error("Error completing bulk payment:", error);
      toast.error("Failed to complete bulk payment");
    }
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard`);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const PaymentTable = ({ payments, isPending }: { payments: WriterPayment[]; isPending: boolean }) => (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Assignment</TableHead>
          <TableHead>Writer</TableHead>
          <TableHead>Client Payment</TableHead>
          <TableHead>Writer Amount</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Date</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {payments.map((payment) => (
          <TableRow key={payment.id}>
            <TableCell>
              <div>
                <p className="font-medium">{payment.assignment.title}</p>
                <p className="text-sm text-muted-foreground">#{payment.assignment.taskId}</p>
              </div>
            </TableCell>
            <TableCell>
              <div>
                <p className="font-medium">{payment.writer.name || "N/A"}</p>
                <p className="text-sm text-muted-foreground">{payment.writer.email}</p>
              </div>
            </TableCell>
            <TableCell>
              <span className="font-semibold text-blue-600">
                {formatCurrency(payment.assignment.price)}
              </span>
            </TableCell>
            <TableCell>
              <span className="font-semibold text-green-600">
                {formatCurrency(payment.writerCompensation)}
              </span>
            </TableCell>
            <TableCell>
              <Badge variant={isPending ? "secondary" : "default"}>
                {isPending ? "Pending" : "Paid"}
              </Badge>
            </TableCell>
            <TableCell>
              {payment.writerPaymentDate 
                ? format(new Date(payment.writerPaymentDate), "MMM dd, yyyy")
                : format(new Date(payment.assignment.createdAt), "MMM dd, yyyy")
              }
            </TableCell>
            <TableCell>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSelectedPayment(payment);
                    setPaymentDrawerOpen(true);
                  }}
                >
                  <Eye className="h-4 w-4 mr-1" />
                  {isPending ? "Pay" : "View"}
                </Button>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );

  const GroupedPaymentTable = ({ groupedPayments }: { groupedPayments: GroupedWriterPayments[] }) => (
    <div className="space-y-4">
      {groupedPayments.map((group) => (
        <Card key={group.writer.id} className="border-l-4 border-l-primary">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                  <User className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg">{group.writer.name || "N/A"}</h3>
                  <p className="text-sm text-muted-foreground">{group.writer.email}</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm text-muted-foreground">{group.taskCount} task{group.taskCount > 1 ? 's' : ''}</p>
                <p className="text-xl font-bold text-green-600">{formatCurrency(group.totalAmount)}</p>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Collapsible>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                  <span className="text-sm text-muted-foreground">
                    View {group.taskCount} assignment{group.taskCount > 1 ? 's' : ''} details
                  </span>
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-3 mt-4">
                {group.payments.map((payment) => (
                  <div key={payment.id} className="border rounded-lg p-3 bg-muted/30">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <p className="font-medium">{payment.assignment.title}</p>
                        <p className="text-sm text-muted-foreground">#{payment.assignment.taskId}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-green-600">{formatCurrency(payment.writerCompensation)}</p>
                        <p className="text-xs text-muted-foreground">
                          {format(new Date(payment.assignment.createdAt), "MMM dd, yyyy")}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
                <div className="border-t pt-3 mt-3">
                  <div className="flex items-center justify-between">
                    <span className="font-semibold">Total Payment:</span>
                    <span className="text-xl font-bold text-green-600">{formatCurrency(group.totalAmount)}</span>
                  </div>
                </div>
                <Button
                  className="w-full mt-3"
                  onClick={() => {
                    setSelectedGroupedPayment(group);
                    setPaymentDrawerOpen(true);
                  }}
                >
                  <CreditCard className="h-4 w-4 mr-2" />
                  Pay {formatCurrency(group.totalAmount)} via PayPal
                </Button>
              </CollapsibleContent>
            </Collapsible>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  return (
    <div className="flex flex-col gap-4 p-4 md:gap-6 md:p-6">
      <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              {breadcrumbs.map((breadcrumb, index) => (
                <div key={index} className="flex items-center gap-2">
                  {index > 0 && <BreadcrumbSeparator />}
                  <BreadcrumbItem>
                    {breadcrumb.isCurrentPage ? (
                      <BreadcrumbPage>{breadcrumb.label}</BreadcrumbPage>
                    ) : (
                      <BreadcrumbLink href={breadcrumb.href}>
                        {breadcrumb.label}
                      </BreadcrumbLink>
                    )}
                  </BreadcrumbItem>
                </div>
              ))}
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>

      <div className="space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold">Writer Payments</h1>
          <p className="text-muted-foreground">
            Manage payments to writers for completed assignments
          </p>
        </div>

        {/* Summary Cards */}
        <div className="flex justify-center">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 max-w-4xl w-full">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Payments</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pendingPayments.length}</div>
              <p className="text-xs text-muted-foreground">
                {formatCurrency(pendingPayments.reduce((sum, p) => sum + p.writerCompensation, 0))} total
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed Payments</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{approvedPayments.length}</div>
              <p className="text-xs text-muted-foreground">
                {formatCurrency(approvedPayments.reduce((sum, p) => sum + p.writerCompensation, 0))} total
              </p>
            </CardContent>
          </Card>
          </div>
        </div>

        {/* Payment Tables */}
        <div className="flex justify-center">
          <Tabs defaultValue="pending" className="space-y-4 w-full max-w-6xl">
            <div className="flex justify-center">
              <TabsList className="grid w-full max-w-md grid-cols-2 bg-muted/50 dark:bg-muted/50">
                <TabsTrigger
                  value="pending"
                  className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300"
                >
                  <Clock className="h-4 w-4" />
                  Pending ({pendingPayments.length})
                </TabsTrigger>
                <TabsTrigger
                  value="approved"
                  className="flex items-center gap-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground text-muted-foreground hover:bg-primary/10 hover:text-primary dark:text-muted-foreground dark:hover:text-primary dark:data-[state=active]:bg-primary dark:data-[state=active]:text-primary-foreground transition-all duration-300"
                >
                  <CheckCircle className="h-4 w-4" />
                  Approved ({approvedPayments.length})
                </TabsTrigger>
              </TabsList>
            </div>

          <TabsContent value="pending">
            <Card>
              <CardHeader>
                <CardTitle>Pending Writer Payments</CardTitle>
                <CardDescription>
                  Assignments completed by writers awaiting payment (grouped by writer)
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : groupedPendingPayments.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No pending payments found
                  </div>
                ) : (
                  <GroupedPaymentTable groupedPayments={groupedPendingPayments} />
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="approved">
            <Card>
              <CardHeader>
                <CardTitle>Approved Writer Payments</CardTitle>
                <CardDescription>
                  Completed payments to writers
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : approvedPayments.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No approved payments found
                  </div>
                ) : (
                  <PaymentTable payments={approvedPayments} isPending={false} />
                )}
              </CardContent>
            </Card>
          </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Payment Drawer */}
      <Drawer open={paymentDrawerOpen} onOpenChange={setPaymentDrawerOpen} direction="bottom">
        <DrawerContent className="max-h-[90vh] border-t-2 border-primary/20 bg-gradient-to-b from-background to-background/95 backdrop-blur-sm">
          <div className="mx-auto w-full max-w-4xl">
            {/* Drawer Handle */}
            <div className="flex justify-center pt-3 pb-2">
              <div className="w-12 h-1.5 bg-muted-foreground/30 rounded-full" />
            </div>

            <DrawerHeader className="text-center pb-4 border-b border-border/50">
              <DrawerTitle className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                {selectedGroupedPayment
                  ? `Process Bulk Payment - ${selectedGroupedPayment.taskCount} Tasks`
                  : selectedPayment?.isWriterPaid
                    ? "Payment Details"
                    : "Process Writer Payment"
                }
              </DrawerTitle>
              <DrawerDescription className="text-base text-muted-foreground mt-2">
                {selectedGroupedPayment
                  ? `Pay ${formatCurrency(selectedGroupedPayment.totalAmount)} for ${selectedGroupedPayment.taskCount} completed assignments`
                  : selectedPayment?.isWriterPaid
                    ? "View payment information and transaction details"
                    : "Complete payment to writer via PayPal or M-Pesa"
                }
              </DrawerDescription>
            </DrawerHeader>

            <div className="px-6 py-4 overflow-y-auto max-h-[calc(90vh-160px)] scrollbar-thin scrollbar-thumb-muted-foreground/20 scrollbar-track-transparent">
              {selectedGroupedPayment ? (
                <div className="space-y-8 pb-8">
                  {/* Grouped Payment Content */}
                  <Card className="border-primary/10 bg-gradient-to-br from-primary/5 to-transparent drawer-card-enter">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <div className="w-2 h-2 bg-primary rounded-full"></div>
                        Writer Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Name</Label>
                          <p className="font-semibold text-foreground">{selectedGroupedPayment.writer.name || "N/A"}</p>
                        </div>
                        <div className="space-y-2">
                          <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Email (PayPal)</Label>
                          <div className="flex items-center gap-2 p-2 bg-muted/50 rounded-lg">
                            <p className="font-mono text-sm flex-1">{selectedGroupedPayment.writer.email}</p>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 hover:bg-primary/10"
                              onClick={() => copyToClipboard(selectedGroupedPayment.writer.email, "Email")}
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                        {selectedGroupedPayment.writer.phone && (
                          <div className="md:col-span-2 space-y-2">
                            <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Phone (M-Pesa Alternative)</Label>
                            <div className="flex items-center gap-3 p-3 bg-green-50/50 dark:bg-green-950/20 rounded-lg border border-green-200/50 dark:border-green-800/50">
                              <Phone className="h-4 w-4 text-green-600 dark:text-green-400" />
                              <p className="font-mono text-sm flex-1">{selectedGroupedPayment.writer.phone}</p>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0 hover:bg-green-100 dark:hover:bg-green-900/50"
                                onClick={() => copyToClipboard(selectedGroupedPayment.writer.phone!, "Phone")}
                              >
                                <Copy className="h-3 w-3" />
                              </Button>
                              <Badge variant="secondary" className="text-xs bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300">
                                Kenya M-Pesa
                              </Badge>
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Assignment List */}
                  <Card className="border-blue-200/50 dark:border-blue-800/50 bg-gradient-to-br from-blue-50/50 dark:from-blue-950/20 to-transparent drawer-card-enter">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        Assignment Details ({selectedGroupedPayment.taskCount} tasks)
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3 max-h-60 overflow-y-auto">
                        {selectedGroupedPayment.payments.map((payment) => (
                          <div key={payment.id} className="border rounded-lg p-3 bg-muted/30">
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <p className="font-medium">{payment.assignment.title}</p>
                                <p className="text-sm text-muted-foreground">#{payment.assignment.taskId}</p>
                              </div>
                              <div className="text-right">
                                <p className="font-semibold text-green-600">{formatCurrency(payment.writerCompensation)}</p>
                                <p className="text-xs text-muted-foreground">
                                  {format(new Date(payment.assignment.createdAt), "MMM dd, yyyy")}
                                </p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                      <div className="border-t pt-4 mt-4">
                        <div className="flex items-center justify-between">
                          <span className="text-lg font-semibold">Total Payment:</span>
                          <span className="text-2xl font-bold text-green-600">{formatCurrency(selectedGroupedPayment.totalAmount)}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Payment Options for Grouped Payment */}
                  <Card className="border-orange-200 dark:border-orange-800 bg-gradient-to-br from-orange-50/50 dark:from-orange-950/20 to-transparent drawer-card-enter">
                    <CardHeader className="pb-4">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                        Payment Options
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {/* PayPal Payment */}
                      <div className="border rounded-lg p-4 space-y-4">
                        <div className="flex items-center gap-2">
                          <CreditCard className="h-5 w-5 text-blue-600" />
                          <span className="font-medium">PayPal Bulk Payout</span>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Send {formatCurrency(selectedGroupedPayment.totalAmount)} directly from your business account to writer: {selectedGroupedPayment.writer.email}
                        </p>

                        <WriterPayPalButton
                          orderId={`bulk_${selectedGroupedPayment.writer.id}_${Date.now()}`}
                          amount={selectedGroupedPayment.totalAmount}
                          payeeEmail={selectedGroupedPayment.writer.email}
                          onSuccess={(details) => handleBulkPaymentSuccess(selectedGroupedPayment, details)}
                          onError={(error) => {
                            console.error("PayPal payment error:", error);
                            if (error.message.includes("PayPal Sandbox Limitation")) {
                              toast.error("PayPal Sandbox Issue: Please use M-Pesa option below for testing.");
                            } else {
                              toast.error("Payment failed. Please try again or use M-Pesa option.");
                            }
                          }}
                          onCancel={() => {
                            toast.info("Payment cancelled");
                          }}
                        />
                      </div>

                      {/* M-Pesa Alternative */}
                      {selectedGroupedPayment.writer.phone && (
                        <div className="space-y-4">
                          <Card className="border-green-200 dark:border-green-800 bg-gradient-to-br from-green-50/50 dark:from-green-950/20 to-transparent">
                            <CardHeader className="pb-3">
                              <CardTitle className="text-lg flex items-center gap-2">
                                <Phone className="h-5 w-5 text-green-600 dark:text-green-400" />
                                <span className="text-green-700 dark:text-green-300">M-Pesa Alternative (Kenya)</span>
                              </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                              <div className="bg-green-100/50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                                <p className="text-sm text-green-700 dark:text-green-300 mb-2">
                                  <strong>Send Payment To:</strong>
                                </p>
                                <div className="flex items-center gap-3">
                                  <p className="font-mono text-lg font-semibold text-green-800 dark:text-green-200">
                                    {selectedGroupedPayment.writer.phone}
                                  </p>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0 hover:bg-green-200 dark:hover:bg-green-800"
                                    onClick={() => copyToClipboard(selectedGroupedPayment.writer.phone!, "Phone")}
                                  >
                                    <Copy className="h-3 w-3" />
                                  </Button>
                                </div>
                                <p className="text-sm text-green-600 dark:text-green-400 mt-2">
                                  Amount: <strong>{formatCurrency(selectedGroupedPayment.totalAmount)}</strong>
                                </p>
                                <p className="text-xs text-green-500 dark:text-green-400 mt-1">
                                  For {selectedGroupedPayment.taskCount} completed assignments
                                </p>
                              </div>

                              <div className="bg-amber-50 dark:bg-amber-950/20 p-4 rounded-lg border border-amber-200 dark:border-amber-800">
                                <p className="text-sm text-amber-700 dark:text-amber-300">
                                  <strong>Instructions:</strong> After completing the M-Pesa payment, click the button below to mark all {selectedGroupedPayment.taskCount} payments as completed in the system.
                                </p>
                              </div>

                              <Button
                                className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 text-base shadow-lg hover:shadow-xl transition-all duration-200"
                                onClick={async () => {
                                  try {
                                    // Mark all payments in the group as completed
                                    const promises = selectedGroupedPayment.payments.map(payment =>
                                      fetch(`/api/admin/writer-payments/${payment.id}/complete`, {
                                        method: "POST",
                                        headers: { "Content-Type": "application/json" },
                                        body: JSON.stringify({
                                          manualPayment: true,
                                          paymentMethod: "mpesa",
                                          bulkPayment: true,
                                          totalAmount: selectedGroupedPayment.totalAmount,
                                        }),
                                      })
                                    );

                                    const responses = await Promise.all(promises);
                                    const failedPayments = responses.filter(response => !response.ok);

                                    if (failedPayments.length > 0) {
                                      throw new Error(`Failed to mark ${failedPayments.length} payments as completed`);
                                    }

                                    toast.success(`All ${selectedGroupedPayment.taskCount} payments marked as completed! 🎉`);
                                    setPaymentDrawerOpen(false);
                                    setSelectedGroupedPayment(null);
                                    await fetchPayments();
                                  } catch (error) {
                                    console.error("Error marking bulk payment as completed:", error);
                                    toast.error("Failed to mark payments as completed");
                                  }
                                }}
                              >
                                ✓ Mark All {selectedGroupedPayment.taskCount} Payments as Paid (M-Pesa Completed)
                              </Button>
                            </CardContent>
                          </Card>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              ) : selectedPayment && (
                <div className="space-y-8 pb-8">
              {/* Assignment Details */}
              <Card className="border-primary/10 bg-gradient-to-br from-primary/5 to-transparent drawer-card-enter">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    Assignment Details
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Title</Label>
                      <p className="font-semibold text-foreground">{selectedPayment.assignment.title}</p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Task ID</Label>
                      <p className="font-mono text-sm bg-muted px-2 py-1 rounded">#{selectedPayment.assignment.taskId}</p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Status</Label>
                      <Badge variant="outline" className="bg-green-50 dark:bg-green-950/20 border-green-200 dark:border-green-800">
                        {selectedPayment.assignment.status}
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Amount Due</Label>
                      <p className="font-bold text-xl text-green-600 dark:text-green-400">
                        {formatCurrency(selectedPayment.writerCompensation)}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Writer Details */}
              <Card className="border-blue-200/50 dark:border-blue-800/50 bg-gradient-to-br from-blue-50/50 dark:from-blue-950/20 to-transparent drawer-card-enter">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Writer Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Name</Label>
                      <p className="font-semibold text-foreground">{selectedPayment.writer.name || "N/A"}</p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Email (PayPal)</Label>
                      <div className="flex items-center gap-2 p-2 bg-muted/50 rounded-lg">
                        <p className="font-mono text-sm flex-1">{selectedPayment.writer.email}</p>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 hover:bg-primary/10"
                          onClick={() => copyToClipboard(selectedPayment.writer.email, "Email")}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    {selectedPayment.writer.phone && (
                      <div className="md:col-span-2 space-y-2">
                        <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Phone (M-Pesa Alternative)</Label>
                        <div className="flex items-center gap-3 p-3 bg-green-50/50 dark:bg-green-950/20 rounded-lg border border-green-200/50 dark:border-green-800/50">
                          <Phone className="h-4 w-4 text-green-600 dark:text-green-400" />
                          <p className="font-mono text-sm flex-1">{selectedPayment.writer.phone}</p>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 hover:bg-green-100 dark:hover:bg-green-900/50"
                            onClick={() => copyToClipboard(selectedPayment.writer.phone!, "Phone")}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                          <Badge variant="secondary" className="text-xs bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300">
                            Kenya M-Pesa
                          </Badge>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {selectedPayment.isWriterPaid ? (
                // Payment completed view
                <Card className="border-green-200 dark:border-green-800 bg-gradient-to-br from-green-50 dark:from-green-950/30 to-transparent drawer-card-enter">
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="p-2 bg-green-100 dark:bg-green-900/50 rounded-full">
                        <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
                      </div>
                      <div>
                        <h3 className="font-bold text-green-700 dark:text-green-400 text-lg">Payment Completed</h3>
                        <p className="text-sm text-green-600 dark:text-green-300">
                          Successfully processed on{" "}
                          {selectedPayment.writerPaymentDate &&
                            format(new Date(selectedPayment.writerPaymentDate), "MMM dd, yyyy 'at' h:mm a")
                          }
                        </p>
                      </div>
                    </div>
                    <div className="bg-green-100/50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                      <p className="text-green-700 dark:text-green-300 font-semibold">
                        Amount: {formatCurrency(selectedPayment.writerCompensation)}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                // Payment processing view
                <Card className="border-orange-200 dark:border-orange-800 bg-gradient-to-br from-orange-50/50 dark:from-orange-950/20 to-transparent drawer-card-enter">
                  <CardHeader className="pb-4">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                      Payment Options
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">

                    {/* PayPal Payment */}
                    <div className="border rounded-lg p-4 space-y-4">
                      <div className="flex items-center gap-2">
                        <CreditCard className="h-5 w-5 text-blue-600" />
                        <span className="font-medium">PayPal Direct Payout</span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Send money directly from your business account to writer: {selectedPayment.writer.email}
                      </p>

                      <WriterPayPalButton
                        orderId={selectedPayment.assignmentId}
                        amount={selectedPayment.writerCompensation}
                        payeeEmail={selectedPayment.writer.email}
                        onSuccess={(details) => handlePaymentSuccess(selectedPayment.id, details)}
                        onError={(error) => {
                          console.error("PayPal payment error:", error);
                          if (error.message.includes("PayPal Sandbox Limitation")) {
                            toast.error("PayPal Sandbox Issue: Please use M-Pesa option below for testing.");
                          } else {
                            toast.error("Payment failed. Please try again or use M-Pesa option.");
                          }
                        }}
                        onCancel={() => {
                          toast.info("Payment cancelled");
                        }}
                      />
                    </div>

                    {/* M-Pesa Alternative */}
                    {selectedPayment.writer.phone && (
                      <div className="space-y-4">
                        <Card className="border-green-200 dark:border-green-800 bg-gradient-to-br from-green-50/50 dark:from-green-950/20 to-transparent">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg flex items-center gap-2">
                              <Phone className="h-5 w-5 text-green-600 dark:text-green-400" />
                              <span className="text-green-700 dark:text-green-300">M-Pesa Alternative (Kenya)</span>
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="space-y-4">
                            <div className="bg-green-100/50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                              <p className="text-sm text-green-700 dark:text-green-300 mb-2">
                                <strong>Send Payment To:</strong>
                              </p>
                              <div className="flex items-center gap-3">
                                <p className="font-mono text-lg font-semibold text-green-800 dark:text-green-200">
                                  {selectedPayment.writer.phone}
                                </p>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0 hover:bg-green-200 dark:hover:bg-green-800"
                                  onClick={() => copyToClipboard(selectedPayment.writer.phone!, "Phone")}
                                >
                                  <Copy className="h-3 w-3" />
                                </Button>
                              </div>
                              <p className="text-sm text-green-600 dark:text-green-400 mt-2">
                                Amount: <strong>{formatCurrency(selectedPayment.writerCompensation)}</strong>
                              </p>
                            </div>

                            <div className="bg-amber-50 dark:bg-amber-950/20 p-4 rounded-lg border border-amber-200 dark:border-amber-800">
                              <p className="text-sm text-amber-700 dark:text-amber-300">
                                <strong>Instructions:</strong> After completing the M-Pesa payment, click the button below to mark this payment as completed in the system.
                              </p>
                            </div>

                            <Button
                              className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 text-base shadow-lg hover:shadow-xl transition-all duration-200"
                              onClick={async () => {
                                try {
                                  const response = await fetch(`/api/admin/writer-payments/${selectedPayment.id}/complete`, {
                                    method: "POST",
                                    headers: { "Content-Type": "application/json" },
                                    body: JSON.stringify({
                                      manualPayment: true,
                                      paymentMethod: "mpesa",
                                    }),
                                  });

                                  if (!response.ok) throw new Error("Failed to mark payment as completed");

                                  toast.success("Payment marked as completed! 🎉");
                                  setPaymentDrawerOpen(false);
                                  await fetchPayments();
                                } catch (error) {
                                  console.error("Error marking payment as completed:", error);
                                  toast.error("Failed to mark payment as completed");
                                }
                              }}
                            >
                              ✓ Mark as Paid (M-Pesa Completed)
                            </Button>
                          </CardContent>
                        </Card>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
                </div>
              )}
            </div>

            <DrawerFooter className="border-t border-border/50 bg-muted/30 px-6 py-3 mt-2">
              <div className="flex justify-center">
                <Button
                  variant="outline"
                  onClick={() => {
                    setPaymentDrawerOpen(false);
                    setSelectedPayment(null);
                    setSelectedGroupedPayment(null);
                  }}
                  className="min-w-[120px] bg-background hover:bg-muted"
                >
                  Close
                </Button>
              </div>
              <div className="h-2"></div> {/* Reduced padding at bottom */}
            </DrawerFooter>
          </div>
        </DrawerContent>
      </Drawer>
    </div>
  );
}
