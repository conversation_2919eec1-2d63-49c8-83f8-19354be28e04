"use client";

import { useState, useEffect, useRef } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Star, GraduationCap, Calendar, Award } from "lucide-react";
import { testimonials } from "../testimonial-data";

interface TestimonialCardProps {
  testimonial: typeof testimonials[0];
  className?: string;
}

const TestimonialCard = ({ testimonial, className = "" }: TestimonialCardProps) => {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < rating ? "text-yellow-500 fill-yellow-500" : "text-muted-foreground/30"
        }`}
      />
    ));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      year: 'numeric'
    });
  };

  return (
    <Card className={`group h-full bg-card/80 backdrop-blur-sm border-border/50 hover:border-primary/20 transition-all duration-300 hover:shadow-xl hover:shadow-primary/5 hover:-translate-y-1 ${className}`}>
      <CardContent className="p-6 h-full flex flex-col">
        {/* Header with avatar and info */}
        <div className="flex items-start gap-4 mb-4">
          <Avatar className="h-12 w-12 border-2 border-background shadow-sm group-hover:scale-105 transition-transform duration-300">
            <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
            <AvatarFallback className="bg-primary/10 text-primary font-semibold">
              {testimonial.name.split(' ').map(n => n[0]).join('')}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <h4 className="font-semibold text-foreground text-sm group-hover:text-primary transition-colors duration-300">
              {testimonial.name}
            </h4>
            <p className="text-xs text-muted-foreground mb-1">{testimonial.role}</p>
            <p className="text-xs text-muted-foreground font-medium">{testimonial.university}</p>
          </div>
          {testimonial.verified && (
            <Badge variant="secondary" className="text-xs bg-green-500/10 text-green-600 border-green-500/20">
              <Award className="w-3 h-3 mr-1" />
              Verified
            </Badge>
          )}
        </div>

        {/* Subject and assignment type */}
        <div className="mb-4">
          <div className="flex items-center gap-2 mb-2">
            <GraduationCap className="w-4 h-4 text-primary" />
            <span className="text-sm font-medium text-foreground">{testimonial.subject}</span>
          </div>
          <Badge variant="outline" className="text-xs">
            {testimonial.assignmentType.replace('_', ' ')}
          </Badge>
        </div>

        {/* Testimonial content */}
        <blockquote className="flex-1 text-muted-foreground text-sm leading-relaxed mb-4 relative">
          <div className="absolute -top-2 -left-2 text-primary/20 text-4xl font-serif">&ldquo;</div>
          <p className="italic pl-4">{testimonial.content}</p>
          <div className="absolute -bottom-2 -right-2 text-primary/20 text-4xl font-serif rotate-180">&ldquo;</div>
        </blockquote>

        {/* Footer with rating, grade, and date */}
        <div className="flex items-center justify-between pt-4 border-t border-border/50">
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1">
              {renderStars(testimonial.rating)}
            </div>
            <span className="text-xs text-muted-foreground">({testimonial.rating}.0)</span>
          </div>
          <div className="flex items-center gap-3 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Award className="w-3 h-3" />
              <span className="font-medium text-green-600">{testimonial.grade}</span>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="w-3 h-3" />
              <span>{formatDate(testimonial.completedAt)}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const TestimonialGrid = () => {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  // Split testimonials into two rows
  const firstRowTestimonials = testimonials.slice(0, Math.ceil(testimonials.length / 2));
  const secondRowTestimonials = testimonials.slice(Math.ceil(testimonials.length / 2));

  return (
    <section 
      ref={sectionRef}
      id="testimonials" 
      className="py-20 bg-gradient-to-b from-background to-muted/20 overflow-hidden"
    >
      <div className="container mx-auto px-4 mb-16">
        <div className={`text-center transform transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          <Badge variant="outline" className="mb-4 px-4 py-2 border-primary/20 bg-primary/5 text-primary">
            Success Stories
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            What Our Students Say
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto text-lg">
            Real feedback from students who achieved academic success with our writing services
          </p>
        </div>
      </div>

      {/* First row - sliding right to left */}
      <div className="relative mb-8 overflow-hidden">
        <div className="flex animate-scroll-right-to-left gap-6 w-max hover:pause-animation">
          {[...firstRowTestimonials, ...firstRowTestimonials].map((testimonial, index) => (
            <div key={`row1-${index}`} className="w-80 sm:w-96 flex-shrink-0">
              <TestimonialCard testimonial={testimonial} />
            </div>
          ))}
        </div>
      </div>

      {/* Second row - sliding left to right */}
      <div className="relative overflow-hidden">
        <div className="flex animate-scroll-left-to-right gap-6 w-max hover:pause-animation">
          {[...secondRowTestimonials, ...secondRowTestimonials].map((testimonial, index) => (
            <div key={`row2-${index}`} className="w-80 sm:w-96 flex-shrink-0">
              <TestimonialCard testimonial={testimonial} />
            </div>
          ))}
        </div>
      </div>

      {/* Gradient overlays for smooth edges */}
      <div className="absolute inset-y-0 left-0 w-16 sm:w-32 bg-gradient-to-r from-background to-transparent pointer-events-none z-10" />
      <div className="absolute inset-y-0 right-0 w-16 sm:w-32 bg-gradient-to-l from-background to-transparent pointer-events-none z-10" />
    </section>
  );
};

export default TestimonialGrid;
