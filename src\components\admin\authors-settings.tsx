"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>alog, DialogTrigger, <PERSON>alogContent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Table, TableHead, TableRow, TableHeader, TableBody, TableCell } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Too<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TooltipTrigger } from "@/components/ui/tooltip";
import { toast } from "sonner";
import { Plus, Edit, Trash2, User, BookOpen, Info, Loader2 } from "lucide-react";

interface Author {
  id: string;
  name: string;
  qualifications: string;
  _count: {
    blogs: number;
  };
}

interface AuthorFormState {
  name: string;
  qualifications: string;
}

const defaultAuthorForm: AuthorFormState = {
  name: "",
  qualifications: "",
};

export function AuthorsSettings() {
  const [authors, setAuthors] = React.useState<Author[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [editId, setEditId] = React.useState<string | null>(null);
  const [form, setForm] = React.useState<AuthorFormState>(defaultAuthorForm);
  const [deletingId, setDeletingId] = React.useState<string | null>(null);

  React.useEffect(() => {
    fetchAuthors();
  }, []);

  async function fetchAuthors() {
    setLoading(true);
    try {
      const res = await fetch("/api/blog/authors");
      if (res.ok) {
        const data = await res.json();
        setAuthors(data);
      } else {
        toast.error("Failed to fetch authors");
      }
    } catch (error) {
      console.error("Error fetching authors:", error);
      toast.error("Failed to fetch authors");
    }
    setLoading(false);
  }

  function resetForm() {
    setForm(defaultAuthorForm);
    setEditId(null);
  }

  function openEditDialog(author: Author) {
    setEditId(author.id);
    setForm({
      name: author.name,
      qualifications: author.qualifications,
    });
    setDialogOpen(true);
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setLoading(true);
    
    try {
      const method = editId ? "PUT" : "POST";
      const url = editId ? `/api/blog/authors/${editId}` : "/api/blog/authors";
      
      const res = await fetch(url, {
        method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(form),
      });

      const data = await res.json();

      if (res.ok) {
        toast.success(editId ? "Author updated successfully" : "Author created successfully");
        setDialogOpen(false);
        resetForm();
        fetchAuthors();
      } else {
        toast.error(data.message || "Failed to save author");
      }
    } catch (error) {
      console.error("Error saving author:", error);
      toast.error("Failed to save author");
    }
    setLoading(false);
  }

  async function handleDelete(id: string, name: string, blogCount: number) {
    if (blogCount > 0) {
      toast.error(`Cannot delete ${name}. This author has ${blogCount} associated blog post(s).`);
      return;
    }

    setDeletingId(id);
    try {
      const res = await fetch(`/api/blog/authors/${id}`, { method: "DELETE" });
      const data = await res.json();

      if (res.ok) {
        toast.success("Author deleted successfully");
        fetchAuthors();
      } else {
        toast.error(data.message || "Failed to delete author");
      }
    } catch (error) {
      console.error("Error deleting author:", error);
      toast.error("Failed to delete author");
    }
    setDeletingId(null);
  }

  return (
    <TooltipProvider>
      <Card className="w-full dark:bg-muted bg-white">
        <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="flex items-center gap-2">
            <User className="h-5 w-5" />
            <CardTitle>Blog Authors</CardTitle>
            <Tooltip>
              <TooltipTrigger>
                <Info className="h-4 w-4 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent>
                <p>Manage authors who can be assigned to blog posts. Authors with existing blog posts cannot be deleted.</p>
              </TooltipContent>
            </Tooltip>
          </div>
          <Dialog open={dialogOpen} onOpenChange={(open) => { setDialogOpen(open); if (!open) resetForm(); }}>
            <DialogTrigger asChild>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button onClick={() => { setEditId(null); setDialogOpen(true); }} className="w-full sm:w-auto">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Author
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Create a new blog author</p>
                </TooltipContent>
              </Tooltip>
            </DialogTrigger>
            <DialogContent className="w-[95vw] sm:max-w-md">
              <DialogHeader>
                <DialogTitle>{editId ? "Edit Author" : "Add New Author"}</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="name">Author Name *</Label>
                  <Input
                    id="name"
                    value={form.name}
                    onChange={(e) => setForm(f => ({ ...f, name: e.target.value }))}
                    placeholder="e.g., Dr. Sarah Johnson"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="qualifications">Qualifications *</Label>
                  <Textarea
                    id="qualifications"
                    value={form.qualifications}
                    onChange={(e) => setForm(f => ({ ...f, qualifications: e.target.value }))}
                    placeholder="e.g., PhD in English Literature, Harvard University. 15+ years of academic writing experience."
                    rows={4}
                    required
                  />
                </div>
                <DialogFooter>
                  <Button type="submit" disabled={loading}>
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {editId ? "Updating..." : "Creating..."}
                      </>
                    ) : (
                      editId ? "Update Author" : "Create Author"
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          {loading && !dialogOpen ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>Loading authors...</span>
            </div>
          ) : (
            <div className="rounded-md border overflow-x-auto">
              <Table className="min-w-[600px]">
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead className="hidden sm:table-cell">Qualifications</TableHead>
                    <TableHead>Blog Posts</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {authors.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-8 text-muted-foreground">
                        No authors found. Create your first author to get started.
                      </TableCell>
                    </TableRow>
                  ) : (
                    authors.map((author) => (
                      <TableRow key={author.id}>
                        <TableCell className="font-medium">
                          <div className="flex flex-col">
                            <span>{author.name}</span>
                            <span className="sm:hidden text-xs text-muted-foreground truncate max-w-[200px]" title={author.qualifications}>
                              {author.qualifications.length > 50
                                ? `${author.qualifications.slice(0, 50)}...`
                                : author.qualifications
                              }
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="hidden sm:table-cell max-w-xs">
                          <div className="truncate" title={author.qualifications}>
                            {author.qualifications.length > 80
                              ? `${author.qualifications.slice(0, 80)}...`
                              : author.qualifications
                            }
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary" className="flex items-center gap-1 w-fit">
                            <BookOpen className="h-3 w-3" />
                            {author._count.blogs}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-end gap-1 sm:gap-2">
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => openEditDialog(author)}
                                  className="w-full sm:w-auto"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Edit author details</p>
                              </TooltipContent>
                            </Tooltip>
                            <AlertDialog>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <AlertDialogTrigger asChild>
                                    <Button
                                      size="sm"
                                      variant="destructive"
                                      disabled={author._count.blogs > 0}
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </AlertDialogTrigger>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>
                                    {author._count.blogs > 0
                                      ? `Cannot delete - has ${author._count.blogs} blog post(s)`
                                      : "Delete author"
                                    }
                                  </p>
                                </TooltipContent>
                              </Tooltip>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Delete Author</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to delete <strong>{author.name}</strong>?
                                    This action cannot be undone and will remove all associated data.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel disabled={deletingId === author.id}>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    className="bg-destructive hover:bg-destructive/90"
                                    onClick={() => handleDelete(author.id, author.name, author._count.blogs)}
                                    disabled={deletingId === author.id}
                                  >
                                    {deletingId === author.id ? (
                                      <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Deleting...
                                      </>
                                    ) : (
                                      "Delete Author"
                                    )}
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </TooltipProvider>
  );
}
