"use client";

import React from "react";
import { motion } from "framer-motion";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { contactFormSchema } from "@/lib/validations";
import { useContactForm } from "@/hooks/use-contact-form";
import type { ContactFormData } from "@/types/api";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Send,
  CheckCircle,
  AlertCircle,
  Clock,
  Zap,
  User,
  Mail,
  Phone,
  MessageSquare,
  Tag,
  Shield
} from "lucide-react";

const urgencyOptions = [
  { value: "low", label: "Low Priority", icon: Clock, color: "bg-green-100 text-green-700" },
  { value: "medium", label: "Medium Priority", icon: AlertCircle, color: "bg-yellow-100 text-yellow-700" },
  { value: "high", label: "High Priority", icon: Zap, color: "bg-red-100 text-red-700" },
];

const categoryOptions = [
  { value: "general", label: "General Inquiry" },
  { value: "support", label: "Technical Support" },
  { value: "billing", label: "Billing & Payment" },
  { value: "technical", label: "Technical Issue" },
  { value: "partnership", label: "Partnership Opportunity" },
];

export function ContactForm() {
  const { submitContactForm, isSubmitting, isSubmitted } = useContactForm();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<ContactFormData>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      urgency: "medium",
      category: "general",
      agreeToPrivacy: false,
    },
  });

  const watchedValues = watch();

  const onSubmit = async (data: ContactFormData) => {
    try {
      await submitContactForm(data);
      reset();
    } catch {
      // Error is handled by the hook
    }
  };

  if (isSubmitted) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
      >
        <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/20">
          <CardContent className="p-8 text-center">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            >
              <CheckCircle className="w-16 h-16 text-green-600 dark:text-green-400 mx-auto mb-4" />
            </motion.div>
            <h3 className="text-2xl font-bold text-green-800 dark:text-green-200 mb-2">Message Sent Successfully!</h3>
            <p className="text-green-700 dark:text-green-300 mb-4">
              Thank you for reaching out. We$apos;ve received your message and will get back to you soon.
            </p>
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
              className="border-green-300 text-green-700 hover:bg-green-100 dark:border-green-700 dark:text-green-300 dark:hover:bg-green-900/20"
            >
              Send Another Message
            </Button>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="shadow-lg border-0 dark:border dark:border-gray-700">
        <CardHeader className="pb-6">
          <CardTitle className="text-2xl font-bold flex items-center gap-2 dark:text-gray-100">
            <MessageSquare className="w-6 h-6 text-primary" />
            Send us a Message
          </CardTitle>
          <CardDescription className="text-base dark:text-gray-400">
            Fill out the form below and we$apos;ll get back to you within 24 hours.
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Personal Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name" className="flex items-center gap-2">
                  <User className="w-4 h-4" />
                  Full Name *
                </Label>
                <Input
                  id="name"
                  {...register("name")}
                  placeholder="Enter your full name"
                  className={errors.name ? "border-red-500" : ""}
                />
                {errors.name && (
                  <p className="text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="email" className="flex items-center gap-2">
                  <Mail className="w-4 h-4" />
                  Email Address *
                </Label>
                <Input
                  id="email"
                  type="email"
                  {...register("email")}
                  placeholder="Enter your email address"
                  className={errors.email ? "border-red-500" : ""}
                />
                {errors.email && (
                  <p className="text-sm text-red-600">{errors.email.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone" className="flex items-center gap-2">
                <Phone className="w-4 h-4" />
                Phone Number (Optional)
              </Label>
              <Input
                id="phone"
                {...register("phone")}
                placeholder="Enter your phone number"
              />
            </div>

            {/* Category and Urgency */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Tag className="w-4 h-4" />
                  Category *
                </Label>
                <Select
                  value={watchedValues.category}
                  onValueChange={(value) => setValue("category", value as ContactFormData["category"])}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categoryOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.category && (
                  <p className="text-sm text-red-600">{errors.category.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <AlertCircle className="w-4 h-4" />
                  Priority Level *
                </Label>
                <div className="flex gap-2">
                  {urgencyOptions.map((option) => {
                    const Icon = option.icon;
                    const isSelected = watchedValues.urgency === option.value;
                    return (
                      <Button
                        key={option.value}
                        type="button"
                        variant={isSelected ? "default" : "outline"}
                        size="sm"
                        onClick={() => {
                          setValue("urgency", option.value as ContactFormData["urgency"]);
                        }}
                        className={`flex-1 ${isSelected ? "" : "hover:bg-gray-50"}`}
                      >
                        <Icon className="w-4 h-4 mr-1" />
                        {option.label.split(" ")[0]}
                      </Button>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Subject */}
            <div className="space-y-2">
              <Label htmlFor="subject">Subject *</Label>
              <Input
                id="subject"
                {...register("subject")}
                placeholder="Brief description of your inquiry"
                className={errors.subject ? "border-red-500" : ""}
              />
              {errors.subject && (
                <p className="text-sm text-red-600">{errors.subject.message}</p>
              )}
            </div>

            {/* Message */}
            <div className="space-y-2">
              <Label htmlFor="message">Message *</Label>
              <Textarea
                id="message"
                {...register("message")}
                placeholder="Please provide detailed information about your inquiry..."
                rows={6}
                className={`resize-none ${errors.message ? "border-red-500" : ""}`}
              />
              {errors.message && (
                <p className="text-sm text-red-600">{errors.message.message}</p>
              )}
              <p className="text-sm text-gray-500">
                {watchedValues.message?.length || 0}/2000 characters
              </p>
            </div>

            {/* Privacy Agreement */}
            <div className="flex items-start space-x-2">
              <Checkbox
                id="privacy"
                checked={watchedValues.agreeToPrivacy}
                onCheckedChange={(checked) => setValue("agreeToPrivacy", checked as boolean)}
                className={errors.agreeToPrivacy ? "border-red-500" : ""}
              />
              <Label htmlFor="privacy" className="text-sm leading-5 cursor-pointer">
                <Shield className="w-4 h-4 inline mr-1" />
                I agree to the privacy policy and consent to being contacted regarding my inquiry. *
              </Label>
            </div>
            {errors.agreeToPrivacy && (
              <p className="text-sm text-red-600 ml-6">{errors.agreeToPrivacy.message}</p>
            )}

            {/* Submit Button */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                type="submit"
                disabled={isSubmitting}
                className="w-full h-12 text-lg font-semibold"
              >
                {isSubmitting ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                    Sending Message...
                  </>
                ) : (
                  <>
                    <Send className="w-5 h-5 mr-2" />
                    Send Message
                  </>
                )}
              </Button>
            </motion.div>
          </form>
        </CardContent>
      </Card>
    </motion.div>
  );
}
