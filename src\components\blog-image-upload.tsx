import * as React from 'react';
import { useState, useCallback } from 'react';
import { ImageUpload } from '@/components/image-upload';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface BlogImageUploadProps {
  onImageUpload: (url: string, altText: string) => void;
  currentImageUrl?: string;
  currentAltText?: string;
  className?: string;
  required?: boolean;
}

export const BlogImageUpload: React.FC<BlogImageUploadProps> = ({
  onImageUpload,
  currentImageUrl,
  currentAltText = '',
  className = '',
  required = true
}) => {
  const [imageUrl, setImageUrl] = useState<string>(currentImageUrl || '');
  const [altText, setAltText] = useState<string>(currentAltText);

  const handleImageUpload = useCallback((url: string) => {
    setImageUrl(url);
    // Call parent callback with both URL and alt text
    onImageUpload(url, altText);
  }, [altText, onImageUpload]);

  const handleAltTextChange = useCallback((newAltText: string) => {
    setAltText(newAltText);
    // If we already have an image, update the parent with new alt text
    if (imageUrl) {
      onImageUpload(imageUrl, newAltText);
    }
  }, [imageUrl, onImageUpload]);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          Featured Image
          {required && <span className="text-red-500">*</span>}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <ImageUpload
          onImageUpload={handleImageUpload}
          onAltTextChange={handleAltTextChange}
          currentImageUrl={currentImageUrl}
          currentAltText={currentAltText}
          showAltTextInput={true}
          className="w-full"
        />
        {!imageUrl && required && (
          <p className="text-sm text-muted-foreground">
            Please upload a featured image for your blog post
          </p>
        )}
        {imageUrl && !altText && (
          <p className="text-sm text-orange-600 bg-orange-50 p-2 rounded">
            ⚠️ Please add alt text for better SEO and accessibility
          </p>
        )}
      </CardContent>
    </Card>
  );
};
