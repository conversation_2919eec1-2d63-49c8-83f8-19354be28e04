"use client";

import { useState } from "react";
import { ChevronDown, ChevronUp, Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
// import { cn } from "@/lib/utils";

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: string | null;
  order: number;
  createdAt: string;
  updatedAt: string;
}

interface FAQListProps {
  faqs: FAQ[];
  searchQuery: string;
}

export function FAQList({ faqs, searchQuery }: FAQListProps) {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const toggleExpanded = (id: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedItems(newExpanded);
  };

  const highlightText = (text: string, query: string) => {
    if (!query.trim()) return text;
    
    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">
          {part}
        </mark>
      ) : part
    );
  };

  if (faqs.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="flex justify-center mb-4">
          <div className="p-4 bg-muted rounded-full">
            <Search className="h-8 w-8 text-muted-foreground" />
          </div>
        </div>
        <h3 className="text-lg font-semibold text-foreground mb-2">
          No FAQs Found
        </h3>
        <p className="text-muted-foreground">
          {searchQuery 
            ? `No questions match your search for "${searchQuery}". Try different keywords or browse all categories.`
            : "No FAQs are available in this category."
          }
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-foreground">
          {searchQuery ? "Search Results" : "Questions & Answers"}
        </h2>
        <Badge variant="outline" className="text-sm">
          {faqs.length} {faqs.length === 1 ? "Question" : "Questions"}
        </Badge>
      </div>

      <div className="space-y-3">
        {faqs.map((faq, index) => {
          const isExpanded = expandedItems.has(faq.id);
          
          return (
            <Card key={faq.id} className="transition-all duration-200 hover:shadow-md">
              <CardContent className="p-0">
                <Button
                  variant="ghost"
                  onClick={() => toggleExpanded(faq.id)}
                  className="w-full p-4 h-auto text-left justify-between hover:bg-muted/50"
                >
                  <div className="flex-1 space-y-2">
                    <div className="flex items-start gap-3">
                      <Badge variant="outline" className="text-xs mt-1 shrink-0">
                        {index + 1}
                      </Badge>
                      <div className="flex-1">
                        <h3 className="font-medium text-foreground text-left leading-relaxed">
                          {highlightText(faq.question, searchQuery)}
                        </h3>
                        {faq.category && (
                          <Badge variant="secondary" className="text-xs mt-2">
                            {faq.category}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="ml-4 shrink-0">
                    {isExpanded ? (
                      <ChevronUp className="h-5 w-5 text-muted-foreground" />
                    ) : (
                      <ChevronDown className="h-5 w-5 text-muted-foreground" />
                    )}
                  </div>
                </Button>
                
                {isExpanded && (
                  <div className="px-4 pb-4 pt-0">
                    <div className="pl-8 border-l-2 border-primary/20 ml-6">
                      <div className="pl-4 prose prose-sm max-w-none dark:prose-invert">
                        <p className="text-muted-foreground leading-relaxed whitespace-pre-wrap">
                          {highlightText(faq.answer, searchQuery)}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
