"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface FAQFiltersProps {
  categories: string[];
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
  totalCount: number;
  filteredCount: number;
}

export function FAQFilters({
  categories,
  selectedCategory,
  onCategoryChange,
  totalCount,
  filteredCount,
}: FAQFiltersProps) {
  const allCategories = ["all", ...categories];

  const getCategoryLabel = (category: string) => {
    if (category === "all") return "All Questions";
    return category;
  };

  const getCategoryCount = (category: string) => {
    if (category === "all") return totalCount;
    // This would ideally come from the API, but for now we'll show filtered count
    return category === selectedCategory ? filteredCount : 0;
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <h3 className="font-semibold text-foreground">Filter by Category</h3>
        <div className="space-y-1">
          {allCategories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "ghost"}
              size="sm"
              onClick={() => onCategoryChange(category)}
              className={cn(
                "w-full justify-start text-left h-auto py-2 px-3",
                selectedCategory === category
                  ? "bg-primary text-primary-foreground"
                  : "hover:bg-muted"
              )}
            >
              <div className="flex items-center justify-between w-full">
                <span className="text-sm">
                  {getCategoryLabel(category)}
                </span>
                <Badge 
                  variant={selectedCategory === category ? "secondary" : "outline"}
                  className="ml-2 text-xs"
                >
                  {category === "all" ? totalCount : getCategoryCount(category)}
                </Badge>
              </div>
            </Button>
          ))}
        </div>
      </div>

      {selectedCategory !== "all" && (
        <div className="pt-2 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onCategoryChange("all")}
            className="w-full"
          >
            Clear Filter
          </Button>
        </div>
      )}

      <div className="text-xs text-muted-foreground pt-2 border-t">
        Showing {filteredCount} of {totalCount} questions
      </div>
    </div>
  );
}
