"use client";

import { useState, useEffect, useCallback } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { Button } from "@/components/ui/button";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  Clock,
  DollarSign,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  Eye,
  Calendar,
  AlertCircle
} from "lucide-react";
import { toast } from "sonner";
import { format } from "date-fns";
import { useCurrentUserId } from "@/hooks/use-session-user-id";

interface WriterPayment {
  id: string;
  assignmentId: string;
  writerId: string;
  writerCompensation: number;
  isWriterPaid: boolean;
  writerPaypalEmail?: string;
  assignment: {
    id: string;
    title: string;
    taskId: string;
    status: string;
    createdAt: string;
    updatedAt: string;
  };
}

export default function WriterPendingPaymentsPage() {
  const [payments, setPayments] = useState<WriterPayment[]>([]);
  const [loading, setLoading] = useState(true);
  const { userId } = useCurrentUserId();

  const breadcrumbs = [
    { label: "Dashboard", href: "/writer/dashboard" },
    { label: "Payments", href: "/writer/payments/pending" },
    { label: "Pending", isCurrentPage: true },
  ];

  const fetchPendingPayments = useCallback(async () => {
    try {
      setLoading(true);
      console.log("[Pending Payments] Fetching with userId:", userId);

      // First, test the debug auth endpoint
      const authResponse = await fetch('/api/debug/auth');
      const authData = await authResponse.json();
      console.log("[Pending Payments] Auth debug:", authData);

      // Use debug endpoint temporarily
      const response = await fetch(`/api/debug/writer-payments?status=pending&writerId=${userId}`);
      console.log("[Pending Payments] Response status:", response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error("[Pending Payments] Error response:", errorData);
        throw new Error(errorData.message || "Failed to fetch payments");
      }

      const data = await response.json();
      console.log("[Pending Payments] Response data:", data);

      if (data.success) {
        setPayments(data.data.pending || []);
      }
    } catch (error) {
      console.error("Error fetching pending payments:", error);
      toast.error("Failed to fetch pending payments");
    } finally {
      setLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    if (userId) {
      fetchPendingPayments();
    }
  }, [userId, fetchPendingPayments]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const totalPendingAmount = payments.reduce((sum, payment) => sum + payment.writerCompensation, 0);

  return (
    <div className="flex flex-col gap-4 p-4 md:gap-6 md:p-6">
      <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              {breadcrumbs.map((breadcrumb, index) => (
                <div key={index} className="flex items-center gap-2">
                  {index > 0 && <BreadcrumbSeparator />}
                  <BreadcrumbItem>
                    {breadcrumb.isCurrentPage ? (
                      <BreadcrumbPage>{breadcrumb.label}</BreadcrumbPage>
                    ) : (
                      <BreadcrumbLink href={breadcrumb.href}>
                        {breadcrumb.label}
                      </BreadcrumbLink>
                    )}
                  </BreadcrumbItem>
                </div>
              ))}
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>

      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Pending Payments</h1>
          <p className="text-muted-foreground">
            Assignments completed and awaiting payment from admin
          </p>
        </div>

        {/* Summary Card */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Payments</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{payments.length}</div>
              <p className="text-xs text-muted-foreground">
                assignments awaiting payment
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Amount Due</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(totalPendingAmount)}
              </div>
              <p className="text-xs text-muted-foreground">
                total compensation pending
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Payments Table */}
        <Card>
          <CardHeader>
            <CardTitle>Pending Payment Details</CardTitle>
            <CardDescription>
              Your completed assignments awaiting payment processing
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : payments.length === 0 ? (
              <div className="text-center py-8">
                <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Pending Payments</h3>
                <p className="text-muted-foreground">
                  You don&apos;t have any completed assignments awaiting payment at the moment.
                </p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Assignment</TableHead>
                    <TableHead>Completion Date</TableHead>
                    <TableHead>Amount Due</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>PayPal Email</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {payments.map((payment) => (
                    <TableRow key={payment.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{payment.assignment.title}</p>
                          <p className="text-sm text-muted-foreground">#{payment.assignment.taskId}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          {format(new Date(payment.assignment.updatedAt), "MMM dd, yyyy")}
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="font-semibold text-green-600">
                          {formatCurrency(payment.writerCompensation)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary" className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          Pending
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm text-muted-foreground">
                          {payment.writerPaypalEmail || "Not set"}
                        </span>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* Information Card */}
        <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/20">
          <CardHeader>
            <CardTitle className="text-blue-700 dark:text-blue-300">Payment Information</CardTitle>
          </CardHeader>
          <CardContent className="text-blue-600 dark:text-blue-400">
            <ul className="space-y-2 text-sm">
              <li>• Payments are processed within 3-5 business days after assignment completion</li>
              <li>• Ensure your PayPal email in your profile is correct to receive payments</li>
              <li>• For Kenyan writers, M-Pesa payments may be available as an alternative</li>
              <li>• Contact admin if you have questions about pending payments</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
