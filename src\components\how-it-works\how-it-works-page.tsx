"use client";

import { motion } from "framer-motion";
import { HowItWorksHero } from "./how-it-works-hero";
import { HowItWorksSteps } from "./how-it-works-steps";
import { HowItWorksFeatures } from "./how-it-works-features";
import { HowItWorksTimeline } from "./how-it-works-timeline";
import { HowItWorksFAQ } from "./how-it-works-faq";
import { HowItWorksCTA } from "./how-it-works-cta";

export function HowItWorksPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Hero Section */}
      <HowItWorksHero />

      {/* Steps Section */}
      <motion.section
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="py-16"
      >
        <HowItWorksSteps />
      </motion.section>

      {/* Timeline Section */}
      <motion.section
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="py-16 bg-muted/30"
      >
        <HowItWorksTimeline />
      </motion.section>

      {/* Features Section */}
      <motion.section
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="py-16"
      >
        <HowItWorksFeatures />
      </motion.section>

      {/* FAQ Section */}
      <motion.section
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="py-16 bg-muted/30"
      >
        <HowItWorksFAQ />
      </motion.section>

      {/* Call to Action Section */}
      <motion.section
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="py-16"
      >
        <HowItWorksCTA />
      </motion.section>
    </div>
  );
}
