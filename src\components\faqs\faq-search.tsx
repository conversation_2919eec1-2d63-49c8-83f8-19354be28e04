"use client";

import { Search, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

interface FAQSearchProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

export function FAQSearch({ searchQuery, onSearchChange }: FAQSearchProps) {
  const handleClear = () => {
    onSearchChange("");
  };

  return (
    <div className="space-y-2">
      <h3 className="font-semibold text-foreground">Search FAQs</h3>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          type="text"
          placeholder="Search questions and answers..."
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-10 pr-10"
        />
        {searchQuery && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClear}
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 hover:bg-muted"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>
      {searchQuery && (
        <p className="text-sm text-muted-foreground">
          Searching for &quot;{searchQuery}&quot;
        </p>
      )}
    </div>
  );
}
