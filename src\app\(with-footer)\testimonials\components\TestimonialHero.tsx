"use client";

import { useState, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Star, Users, Award, TrendingUp, Quote } from "lucide-react";
import Link from "next/link";
import { testimonialStats } from "../testimonial-data";

const TestimonialHero = () => {
  const [currentText, setCurrentText] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  const rotatingTexts = [
    "Real Students, Real Success Stories",
    "98% Satisfaction Rate Guaranteed",
    "150,000+ Students Helped Worldwide",
    "Expert Writers, Exceptional Results"
  ];

  useEffect(() => {
    setIsVisible(true);
    const interval = setInterval(() => {
      setCurrentText((prev) => (prev + 1) % rotatingTexts.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [rotatingTexts.length]);

  const stats = [
    {
      icon: Star,
      value: testimonialStats.averageRating.toFixed(1),
      label: "Average Rating",
      color: "text-yellow-500",
      bgColor: "bg-yellow-500/10"
    },
    {
      icon: Users,
      value: testimonialStats.totalStudentsHelped,
      label: "Students Helped",
      color: "text-blue-500",
      bgColor: "bg-blue-500/10"
    },
    {
      icon: Award,
      value: `${testimonialStats.satisfactionRate}%`,
      label: "Satisfaction Rate",
      color: "text-green-500",
      bgColor: "bg-green-500/10"
    },
    {
      icon: TrendingUp,
      value: testimonialStats.expertWriters,
      label: "Expert Writers",
      color: "text-purple-500",
      bgColor: "bg-purple-500/10"
    }
  ];

  return (
    <section className="relative min-h-[80vh] flex items-center justify-center bg-gradient-to-br from-background via-background to-muted/30 overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary/5 rounded-full blur-3xl animate-pulse" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-chart-1/5 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-chart-2/3 rounded-full blur-3xl opacity-10 animate-spin-slow" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center max-w-4xl mx-auto">
          {/* Badge */}
          <div className={`transform transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
            <Badge 
              variant="outline" 
              className="mb-6 px-6 py-2 text-sm font-medium border-primary/20 bg-primary/5 text-primary hover:bg-primary/10 transition-colors"
            >
              <Quote className="w-4 h-4 mr-2" />
              Verified Student Reviews
            </Badge>
          </div>

          {/* Main heading with rotating text */}
          <div className={`transform transition-all duration-1000 delay-200 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-6 leading-tight">
              Student{" "}
              <span className="text-primary relative">
                Testimonials
                <div className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-primary/50 to-chart-1/50 rounded-full" />
              </span>
            </h1>
            
            <div className="h-16 flex items-center justify-center">
              <p className="text-xl md:text-2xl text-muted-foreground font-medium transition-all duration-500">
                {rotatingTexts[currentText]}
              </p>
            </div>
          </div>

          {/* Description */}
          <div className={`transform transition-all duration-1000 delay-400 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto mb-8 leading-relaxed">
              Discover how our academic writing services have transformed the educational journey of thousands of students worldwide. 
              Read authentic reviews from students who achieved academic excellence with our expert assistance.
            </p>
          </div>

          {/* CTA Buttons */}
          <div className={`transform transition-all duration-1000 delay-600 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Button asChild size="lg" className="px-8 py-3 text-lg font-semibold">
                <Link href="/register/client">
                  Start Your Success Story
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="px-8 py-3 text-lg font-semibold">
                <Link href="#testimonials">
                  Read More Reviews
                </Link>
              </Button>
            </div>
          </div>

          {/* Stats Grid */}
          <div className={`transform transition-all duration-1000 delay-800 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 max-w-4xl mx-auto">
              {stats.map((stat, index) => {
                const Icon = stat.icon;
                return (
                  <div
                    key={index}
                    className="group p-6 rounded-2xl bg-card/50 backdrop-blur-sm border border-border/50 hover:border-primary/20 transition-all duration-300 hover:shadow-lg hover:shadow-primary/5"
                  >
                    <div className={`w-12 h-12 rounded-xl ${stat.bgColor} flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className={`w-6 h-6 ${stat.color}`} />
                    </div>
                    <div className="text-2xl font-bold text-foreground mb-1">
                      {stat.value}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {stat.label}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-muted-foreground/30 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-muted-foreground/50 rounded-full mt-2 animate-pulse" />
        </div>
      </div>
    </section>
  );
};

export default TestimonialHero;
