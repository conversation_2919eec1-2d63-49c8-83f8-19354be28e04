import { PrismaClient, AcademicLevel, Priority, Spacing } from "@prisma/client";

const prisma = new PrismaClient();

// Helper function to generate rule key
function generateRuleKey(
  ruleType: string,
  academicLevel?: AcademicLevel | null,
  priority?: Priority | null,
  spacing?: Spacing | null
): string {
  const parts = [
    ruleType,
    academicLevel || 'null',
    priority || 'null',
    spacing || 'null'
  ];
  return parts.join(':');
}

async function updateExistingPricingRules() {
  try {
    

    // Get all existing pricing rules
    const existingRules = await prisma.pricingRule.findMany();
    
    

    // Update each rule with the proper ruleKey
    for (const rule of existingRules) {
      const ruleKey = generateRuleKey(
        rule.ruleType,
        rule.academicLevel,
        rule.priority,
        rule.spacing
      );

      await prisma.pricingRule.update({
        where: { id: rule.id },
        data: { ruleKey },
      });

      
    }

    
  } catch (error) {
    console.error("Error updating existing pricing rules:", error);
    throw error;
  }
}

async function main() {
 
  await updateExistingPricingRules();

}

main()
  .catch((e) => {
    console.error("Migration failed:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
