// src/lib/pricing-service.ts
import prisma from "./prisma";
import { AcademicLevel, Priority, Spacing } from "@prisma/client";

export interface PricingParams {
  academicLevel: AcademicLevel;
  priority: Priority;
  spacing: Spacing;
  pageCount: number;
}

export interface PriceBreakdown {
  basePrice: number;
  academicLevelMultiplier: number;
  priorityMultiplier: number;
  spacingMultiplier: number;
  subtotal: number;
  finalPrice: number;
  minimumPrice: number;
}

export interface WriterCompensation {
  percentage: number;
  minimumPerPage: number;
  calculatedAmount: number;
  finalAmount: number;
}

class PricingService {
  private pricingRulesCache: Map<string, number> = new Map();
  private cacheExpiry: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  private cacheInvalidationCallbacks: Set<() => void> = new Set();

  /**
   * Get pricing rules from database with caching
   */
  private async getPricingRules(): Promise<Map<string, number>> {
    const now = Date.now();
    
    if (this.cacheExpiry > now && this.pricingRulesCache.size > 0) {
      return this.pricingRulesCache;
    }

    try {
      const rules = await prisma.pricingRule.findMany({
        where: { isActive: true },
      });

      this.pricingRulesCache.clear();
      
      for (const rule of rules) {
        const key = this.buildRuleKey(rule.ruleType, rule.academicLevel, rule.priority, rule.spacing);
        this.pricingRulesCache.set(key, rule.value);
      }

      this.cacheExpiry = now + this.CACHE_DURATION;
      return this.pricingRulesCache;
    } catch (error) {
      console.error("Error fetching pricing rules:", error);
      // Return default values if database fails
      return this.getDefaultPricingRules();
    }
  }

  /**
   * Build cache key for pricing rules
   */
  private buildRuleKey(
    ruleType: string,
    academicLevel: AcademicLevel | null | undefined,
    priority: Priority | null | undefined,
    spacing: Spacing | null | undefined
  ): string {
    return `${ruleType}:${academicLevel || 'null'}:${priority || 'null'}:${spacing || 'null'}`;
  }

  /**
   * Get default pricing rules as fallback
   */
  private getDefaultPricingRules(): Map<string, number> {
    const defaults = new Map<string, number>();
    
    // Base pricing
    defaults.set("base_price:null:null:null", 15.0);
    defaults.set("minimum_price:null:null:null", 10.0);
    
    // Academic level multipliers
    defaults.set("academic_multiplier:HIGH_SCHOOL:null:null", 1.0);
    defaults.set("academic_multiplier:UNDERGRADUATE:null:null", 1.2);
    defaults.set("academic_multiplier:MASTERS:null:null", 1.5);
    defaults.set("academic_multiplier:PHD:null:null", 2.0);
    defaults.set("academic_multiplier:PROFESSIONAL:null:null", 1.8);
    
    // Priority multipliers
    defaults.set("priority_multiplier:null:LOW:null", 1.0);
    defaults.set("priority_multiplier:null:MEDIUM:null", 1.5);
    defaults.set("priority_multiplier:null:HIGH:null", 2.0);
    
    // Spacing multipliers
    defaults.set("spacing_multiplier:null:null:DOUBLE", 1.0);
    defaults.set("spacing_multiplier:null:null:SINGLE", 2.0);
    
    // Writer compensation
    defaults.set("writer_percentage:null:null:null", 0.35);
    defaults.set("writer_minimum_per_page:null:null:null", 3.0);
    
    return defaults;
  }

  /**
   * Calculate assignment price with detailed breakdown
   */
  async calculatePrice(params: PricingParams): Promise<PriceBreakdown> {
    const rules = await this.getPricingRules();
    
    // Get base values
    const basePrice = rules.get("base_price:null:null:null") || 15.0;
    const minimumPrice = rules.get("minimum_price:null:null:null") || 10.0;
    
    // Get multipliers
    const academicLevelMultiplier = rules.get(`academic_multiplier:${params.academicLevel}:null:null`) || 1.0;
    const priorityMultiplier = rules.get(`priority_multiplier:null:${params.priority}:null`) || 1.0;
    const spacingMultiplier = rules.get(`spacing_multiplier:null:null:${params.spacing}`) || 1.0;
    
    // Calculate price
    let subtotal = basePrice * params.pageCount;
    subtotal *= academicLevelMultiplier;
    subtotal *= priorityMultiplier;
    subtotal *= spacingMultiplier;
    
    const finalPrice = Math.max(subtotal, minimumPrice);
    
    return {
      basePrice: basePrice * params.pageCount,
      academicLevelMultiplier,
      priorityMultiplier,
      spacingMultiplier,
      subtotal,
      finalPrice,
      minimumPrice,
    };
  }

  /**
   * Calculate writer compensation
   */
  async calculateWriterCompensation(clientPrice: number, pageCount: number): Promise<WriterCompensation> {
    const rules = await this.getPricingRules();
    
    const percentage = rules.get("writer_percentage:null:null:null") || 0.35;
    const minimumPerPage = rules.get("writer_minimum_per_page:null:null:null") || 3.0;
    
    const calculatedAmount = clientPrice * percentage;
    const minimumAmount = pageCount * minimumPerPage;
    const finalAmount = Math.max(calculatedAmount, minimumAmount);
    
    return {
      percentage,
      minimumPerPage,
      calculatedAmount,
      finalAmount,
    };
  }

  /**
   * Generate a unique rule key for MongoDB compatibility
   */
  private generateRuleKey(
    ruleType: string,
    academicLevel?: AcademicLevel,
    priority?: Priority,
    spacing?: Spacing
  ): string {
    const parts = [
      ruleType,
      academicLevel || 'null',
      priority || 'null',
      spacing || 'null'
    ];
    return parts.join(':');
  }

  /**
   * Update pricing rule
   */
  async updatePricingRule(
    ruleType: string,
    value: number,
    academicLevel?: AcademicLevel,
    priority?: Priority,
    spacing?: Spacing
  ): Promise<boolean> {
    try {
      // Generate unique rule key
      const ruleKey = this.generateRuleKey(ruleType, academicLevel, priority, spacing);

      // Build the create data
      const createData: {
        ruleType: string;
        value: number;
        ruleKey: string;
        academicLevel?: AcademicLevel;
        priority?: Priority;
        spacing?: Spacing;
      } = {
        ruleType,
        value,
        ruleKey,
      };

      // Only add fields if they are defined (not undefined)
      if (academicLevel !== undefined) {
        createData.academicLevel = academicLevel;
      }
      if (priority !== undefined) {
        createData.priority = priority;
      }
      if (spacing !== undefined) {
        createData.spacing = spacing;
      }

      // Use upsert with the ruleKey as unique identifier
      await prisma.pricingRule.upsert({
        where: { ruleKey },
        update: {
          value,
          updatedAt: new Date(),
          // Update other fields in case they changed
          ...(academicLevel !== undefined && { academicLevel }),
          ...(priority !== undefined && { priority }),
          ...(spacing !== undefined && { spacing }),
        },
        create: createData,
      });

      // Clear cache to force refresh
      this.pricingRulesCache.clear();
      this.cacheExpiry = 0;

      return true;
    } catch (error) {
      console.error("Error updating pricing rule:", error);
      return false;
    }
  }

  /**
   * Get all pricing rules for admin interface
   */
  async getAllPricingRules() {
    try {
      return await prisma.pricingRule.findMany({
        where: { isActive: true },
        orderBy: [
          { ruleType: 'asc' },
          { academicLevel: 'asc' },
          { priority: 'asc' },
          { spacing: 'asc' },
        ],
      });
    } catch (error) {
      console.error("Error fetching all pricing rules:", error);
      return [];
    }
  }

  /**
   * Clear pricing cache (useful for testing or manual refresh)
   */
  clearCache(): void {
    this.pricingRulesCache.clear();
    this.cacheExpiry = 0;
    // Notify all subscribers about cache invalidation
    this.notifyCacheInvalidation();
  }

  /**
   * Subscribe to cache invalidation events
   */
  onCacheInvalidation(callback: () => void): () => void {
    this.cacheInvalidationCallbacks.add(callback);
    // Return unsubscribe function
    return () => {
      this.cacheInvalidationCallbacks.delete(callback);
    };
  }

  /**
   * Notify all subscribers about cache invalidation
   */
  private notifyCacheInvalidation(): void {
    this.cacheInvalidationCallbacks.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('Error in cache invalidation callback:', error);
      }
    });
  }
}

// Export singleton instance
export const pricingService = new PricingService();

// Legacy function for backward compatibility
export async function calculateAssignmentPrice(
  academicLevel: AcademicLevel,
  pageCount: number,
  priority: Priority = Priority.MEDIUM,
  spacing: Spacing = Spacing.DOUBLE
): Promise<number> {
  const breakdown = await pricingService.calculatePrice({
    academicLevel,
    priority,
    spacing,
    pageCount,
  });
  
  return breakdown.finalPrice;
}
