'use client';


import { TawkLiveChat, useTawkEvent, TawkEvent } from 'tawk-react';


const TawkChat = () => {
  const handleChatStart = () => {
    console.log('Chat started');
  };

  return (
    <div className="tawk-chat-container">
      

      {/* Tawk.to Chat Widget */}
      <TawkLiveChat
        propertyId={process.env.NEXT_PUBLIC_TAWK_PROPERTY_ID || ''}
        widgetId={process.env.NEXT_PUBLIC_TAWK_WIDGET_ID || ''}
        // visitor={isFormSubmitted ? visitorInfo : undefined}
        customStyle={{ zIndex: 1000 }}
      />

      {/* Chat Event Listener Component */}
      <ChatEventListener onChatStart={handleChatStart} />
    </div>
  );
};

// Separate component to handle chat events
const ChatEventListener = ({ onChatStart }: { onChatStart: () => void }) => {
  useTawkEvent(TawkEvent.onLoad, () => {
    console.log('Tawk.to chat loaded');
  });

  useTawkEvent(TawkEvent.onChatStarted, () => {
    console.log('Chat started');
    onChatStart();
  });

  useTawkEvent(TawkEvent.onChatEnded, () => {
    console.log('Chat ended');
  });

  return null;
};

export default TawkChat;