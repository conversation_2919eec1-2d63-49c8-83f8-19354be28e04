import { useState } from 'react';
import { toast } from 'sonner';

interface UseNewsletterReturn {
  isLoading: boolean;
  isSubscribed: boolean;
  subscribe: (email: string, source?: string) => Promise<boolean>;
  unsubscribe: (email: string) => Promise<boolean>;
}

interface SubscribeResponse {
  success: boolean;
  message: string;
  error?: string;
}

interface UnsubscribeResponse {
  success: boolean;
  message: string;
  error?: string;
}

export function useNewsletter(): UseNewsletterReturn {
  const [isLoading, setIsLoading] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);

  const subscribe = async (email: string, source?: string): Promise<boolean> => {
    if (!email) {
      toast.error('Email is required');
      return false;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      toast.error('Please enter a valid email address');
      return false;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/newsletter/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, source }),
      });

      const data: SubscribeResponse = await response.json();

      if (response.ok && data.success) {
        setIsSubscribed(true);
        toast.success('Successfully subscribed to newsletter! 🎉');
        return true;
      } else {
        if (response.status === 409) {
          toast.info('You are already subscribed to our newsletter');
        } else {
          toast.error(data.error || 'Failed to subscribe to newsletter');
        }
        return false;
      }
    } catch (error) {
      console.error('Newsletter subscription error:', error);
      toast.error('Something went wrong. Please try again.');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const unsubscribe = async (email: string): Promise<boolean> => {
    if (!email) {
      toast.error('Email is required');
      return false;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/newsletter/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data: UnsubscribeResponse = await response.json();

      if (response.ok && data.success) {
        setIsSubscribed(false);
        toast.success('Successfully unsubscribed from newsletter');
        return true;
      } else {
        if (response.status === 404) {
          toast.error('Email not found in our newsletter list');
        } else if (response.status === 409) {
          toast.info('You are already unsubscribed');
        } else {
          toast.error(data.error || 'Failed to unsubscribe from newsletter');
        }
        return false;
      }
    } catch (error) {
      console.error('Newsletter unsubscribe error:', error);
      toast.error('Something went wrong. Please try again.');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    isSubscribed,
    subscribe,
    unsubscribe,
  };
}
