"use client";

import { useEffect, useState } from "react";

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: string | null;
  order: number;
}

interface FAQStructuredDataProps {
  faqs: FAQ[];
}

export function FAQStructuredData({ faqs }: FAQStructuredDataProps) {
  const [companyName, setCompanyName] = useState("Academic Writing Service");

  useEffect(() => {
    // Fetch company info for structured data
    const fetchCompanyInfo = async () => {
      try {
        const response = await fetch("/api/company-info");
        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data.companyName) {
            setCompanyName(result.data.companyName);
          }
        }
      } catch (error) {
        console.error("Error fetching company info for structured data:", error);
      }
    };

    fetchCompanyInfo();
  }, []);

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "name": `Frequently Asked Questions - ${companyName}`,
    "description": `Find answers to frequently asked questions about ${companyName}'s academic writing services.`,
    "mainEntity": faqs.map((faq) => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer,
      },
    })),
    "about": {
      "@type": "Organization",
      "name": companyName,
      "description": "Professional academic writing services",
    },
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData, null, 2),
      }}
    />
  );
}
