"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Mail, CheckCircle, AlertCircle } from "lucide-react";
import { toast } from "sonner";

export default function VerifyEmailRequiredPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isResending, setIsResending] = useState(false);

  useEffect(() => {
    // If user is already verified, redirect to their dashboard
    const handleRedirect = async () => {
      if (session?.user?.emailVerified) {
        const dashboardUrl = await getDashboardUrl(session.user.role);
        router.push(dashboardUrl);
      }
    };

    handleRedirect();
  }, [session, router]);

  const getDashboardUrl = async (role: string) => {
    switch (role) {
      case "ADMIN":
        return "/admin/dashboard";
      case "WRITER": {
        // For writers, check if they are approved
        if (session?.user?.id) {
          try {
            const response = await fetch(`/api/users/writers/${session.user.id}`);
            const data = await response.json();
            const isApproved = data.success && data.data?.isApproved;
            return isApproved ? "/writer/dashboard" : "/writer-assessment";
          } catch (error) {
            console.error("Error checking writer approval status:", error);
            return "/writer-assessment"; // Default to assessment if error
          }
        }
        return "/writer-assessment";
      }
      case "CLIENT":
        return "/client/dashboard";
      default:
        return "/client/dashboard";
    }
  };

  const handleResendVerification = async () => {
    if (!session?.user?.email) {
      toast.error("No email address found");
      return;
    }

    setIsResending(true);
    try {
      const response = await fetch("/api/auth/send-verification-email", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          email: session.user.email,
          userId: session.user.id,
        }),
      });

      if (response.ok) {
        toast.success("Verification email sent! Please check your inbox.");
      } else {
        const errorData = await response.text();
        console.error("📧 Verification email error:", errorData);
        toast.error("Failed to send verification email. Please try again.");
      }
    } catch (error) {
      console.error("📧 Verification email error:", error);
      toast.error("Failed to send verification email. Please try again.");
    } finally {
      setIsResending(false);
    }
  };

  if (status === "loading") {
    return (
      <div className="container flex h-screen w-full items-center justify-center px-4 py-8">
        <Card className="w-full max-w-md">
          <CardContent className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p>Loading...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!session) {
    router.push("/login/client");
    return null;
  }

  return (
    <div className="container flex h-screen w-full items-center justify-center px-4 py-8">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
              <Mail className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <CardTitle className="text-xl font-bold">
            Email Verification Required
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center space-y-2">
            <p className="text-muted-foreground">
              We&apos;ve sent a verification email to:
            </p>
            <p className="font-medium text-primary">
              {session.user.email}
            </p>
            <p className="text-sm text-muted-foreground">
              Please check your inbox and click the verification link to complete your registration.
            </p>
          </div>

          <div className="space-y-3">
            <Button
              onClick={handleResendVerification}
              disabled={isResending}
              className="w-full"
              variant="outline"
            >
              {isResending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                  Sending...
                </>
              ) : (
                <>
                  <Mail className="h-4 w-4 mr-2" />
                  Resend Verification Email
                </>
              )}
            </Button>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
              <AlertCircle className="h-4 w-4" />
              <span>Check your spam folder if you don&apos;t see the email</span>
            </div>
          </div>

          <div className="border-t pt-4">
            <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span>Already verified? The page will redirect automatically</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
