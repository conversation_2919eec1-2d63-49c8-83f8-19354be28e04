import { NextRequest } from 'next/server';
import prisma from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/auth';
import { apiError, apiSuccess } from '@/lib/api-utils';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authConfig);
    if (!session?.user) {
      return apiError('Unauthorized', 401);
    }

    const body = await request.json();
    const { assignmentId, fileName, originalName, fileUrl, fileSize, fileType } = body;

    if (!assignmentId || !fileName || !originalName || !fileUrl || !fileSize || !fileType) {
      return apiError('Missing required fields', 400);
    }

    // Verify assignment exists and user has access
    const assignment = await prisma.assignment.findUnique({
      where: { id: assignmentId },
      include: { client: true, assignedWriter: true }
    });

    if (!assignment) {
      return apiError('Assignment not found', 404);
    }

    // Check if user has permission to link files to this assignment
    const isAuthorized = 
      session.user.role === 'ADMIN' ||
      assignment.clientId === session.user.id ||
      assignment.assignedWriterId === session.user.id;

    if (!isAuthorized) {
      return apiError('Unauthorized to link files to this assignment', 403);
    }

    // Create file attachment record in database
    const fileAttachment = await prisma.fileAttachment.create({
      data: {
        assignmentId,
        fileName,
        originalName,
        fileUrl,
        fileSize,
        fileType
      }
    });

    return apiSuccess({
      id: fileAttachment.id,
      url: fileAttachment.fileUrl,
      name: fileAttachment.originalName,
      size: fileAttachment.fileSize,
      type: fileAttachment.fileType,
      uploadedAt: fileAttachment.uploadedAt.toISOString()
    }, 'File linked successfully');

  } catch (error) {
    console.error('File link error:', error);
    return apiError('Internal server error', 500);
  }
}
