"use client";

import { useState, useCallback } from 'react';

export interface CouponValidationResult {
  isValid: boolean;
  coupon?: {
    id: string;
    code: string;
    description: string;
    discountPercentage: number;
  };
  discountAmount?: number;
  finalPrice?: number;
  error?: string;
}

export interface CouponApplicationResult {
  success: boolean;
  couponUsageId?: string;
  discountAmount?: number;
  finalPrice?: number;
  error?: string;
}

export interface CouponState {
  isApplied: boolean;
  couponCode: string;
  discountAmount: number;
  finalPrice: number;
  originalPrice: number;
  coupon?: {
    id: string;
    code: string;
    description: string;
    discountPercentage: number;
  };
}

export function useCoupon(initialPrice: number = 0) {
  const [couponState, setCouponState] = useState<CouponState>({
    isApplied: false,
    couponCode: '',
    discountAmount: 0,
    finalPrice: initialPrice,
    originalPrice: initialPrice,
  });

  const [isValidating, setIsValidating] = useState(false);
  const [isApplying, setIsApplying] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);

  // Update original price when it changes
  const updateOriginalPrice = useCallback((newPrice: number) => {
    setCouponState(prev => ({
      ...prev,
      originalPrice: newPrice,
      finalPrice: prev.isApplied ? newPrice - prev.discountAmount : newPrice,
    }));
  }, []);

  // Validate coupon code
  const validateCoupon = useCallback(async (code: string, originalPrice: number): Promise<CouponValidationResult> => {
    console.log("🔍 [FRONTEND] Starting coupon validation - Code:", code, "Original Price:", originalPrice);

    if (!code.trim()) {
      console.log("❌ [FRONTEND] Empty coupon code");
      return { isValid: false, error: "Please enter a coupon code" };
    }

    setIsValidating(true);
    setValidationError(null);

    try {
      const requestData = {
        code: code.trim().toUpperCase(),
        originalPrice,
      };

      console.log("🔍 [FRONTEND] Sending validation request:", requestData);

      const response = await fetch('/api/coupons/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      console.log("🔍 [FRONTEND] Response status:", response.status);
      console.log("🔍 [FRONTEND] Response headers:", Object.fromEntries(response.headers.entries()));

      const data = await response.json();
      console.log("🔍 [FRONTEND] Response data:", data);

      if (!response.ok) {
        const error = data.message || 'Failed to validate coupon';
        console.log("❌ [FRONTEND] Validation failed:", error);
        console.log("❌ [FRONTEND] Full error data:", data);
        setValidationError(error);
        return { isValid: false, error };
      }

      console.log("✅ [FRONTEND] Validation successful:", data.data);
      return {
        isValid: true,
        coupon: data.data.coupon,
        discountAmount: data.data.discountAmount,
        finalPrice: data.data.finalPrice,
      };
    } catch (error) {
      console.error('❌ [FRONTEND] Coupon validation network error:', error);
      const errorMessage = 'Network error. Please try again.';
      setValidationError(errorMessage);
      return { isValid: false, error: errorMessage };
    } finally {
      setIsValidating(false);
    }
  }, []);

  // Apply coupon
  const applyCoupon = useCallback(async (code: string, assignmentId?: string): Promise<CouponApplicationResult> => {
    if (!code.trim()) {
      return { success: false, error: "Please enter a coupon code" };
    }

    setIsApplying(true);
    setValidationError(null);

    try {
      // First validate the coupon
      const validation = await validateCoupon(code, couponState.originalPrice);
      
      if (!validation.isValid) {
        return { success: false, error: validation.error };
      }

      // Apply the coupon
      const response = await fetch('/api/coupons/apply', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: code.trim().toUpperCase(),
          originalPrice: couponState.originalPrice,
          assignmentId,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        const error = data.message || 'Failed to apply coupon';
        setValidationError(error);
        return { success: false, error };
      }

      // Update coupon state
      setCouponState(prev => ({
        ...prev,
        isApplied: true,
        couponCode: code.trim().toUpperCase(),
        discountAmount: data.data.discountAmount,
        finalPrice: data.data.finalPrice,
        coupon: validation.coupon,
      }));

      return {
        success: true,
        couponUsageId: data.data.couponUsageId,
        discountAmount: data.data.discountAmount,
        finalPrice: data.data.finalPrice,
      };
    } catch (error) {
      console.error('Coupon application error:', error);
      const errorMessage = 'Network error. Please try again.';
      setValidationError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsApplying(false);
    }
  }, [couponState.originalPrice, validateCoupon]);

  // Remove applied coupon
  const removeCoupon = useCallback(() => {
    setCouponState(prev => ({
      isApplied: false,
      couponCode: '',
      discountAmount: 0,
      finalPrice: prev.originalPrice,
      originalPrice: prev.originalPrice,
      coupon: undefined,
    }));
    setValidationError(null);
  }, []);

  // Reset coupon state
  const resetCoupon = useCallback(() => {
    setCouponState({
      isApplied: false,
      couponCode: '',
      discountAmount: 0,
      finalPrice: initialPrice,
      originalPrice: initialPrice,
    });
    setValidationError(null);
  }, [initialPrice]);

  // Calculate discount percentage
  const getDiscountPercentage = useCallback(() => {
    if (!couponState.isApplied || couponState.originalPrice === 0) {
      return 0;
    }
    return (couponState.discountAmount / couponState.originalPrice) * 100;
  }, [couponState.isApplied, couponState.discountAmount, couponState.originalPrice]);

  // Get savings amount
  const getSavingsAmount = useCallback(() => {
    return couponState.isApplied ? couponState.discountAmount : 0;
  }, [couponState.isApplied, couponState.discountAmount]);

  return {
    // State
    couponState,
    isValidating,
    isApplying,
    validationError,
    
    // Actions
    validateCoupon,
    applyCoupon,
    removeCoupon,
    resetCoupon,
    updateOriginalPrice,
    
    // Computed values
    getDiscountPercentage,
    getSavingsAmount,
    
    // Convenience getters
    isApplied: couponState.isApplied,
    finalPrice: couponState.finalPrice,
    originalPrice: couponState.originalPrice,
    discountAmount: couponState.discountAmount,
    couponCode: couponState.couponCode,
  };
}

// Hook for admin coupon management
export function useAdminCoupons() {
  const [coupons, setCoupons] = useState<Array<{
    id: string;
    code: string;
    description: string;
    discountPercentage: number;
    isActive: boolean;
    maxUses: number | null;
    currentUses: number;
    expiresAt: Date | null;
    createdAt: Date;
    updatedAt: Date;
    usageCount: number;
  }>>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch all coupons
  const fetchCoupons = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin/coupons', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Network error' }));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (!data || typeof data !== 'object') {
        throw new Error('Invalid response format');
      }

      const couponsData = data.data;
      if (!Array.isArray(couponsData)) {
        console.error('Expected coupons array, got:', couponsData);
        setCoupons([]);
        return;
      }

      setCoupons(couponsData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch coupons';
      setError(errorMessage);
      console.error('Error fetching coupons:', err);
      setCoupons([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  }, []);

  // Create coupon
  const createCoupon = useCallback(async (couponData: {
    description: string;
    discountPercentage: number;
    maxUses?: number;
    expiresAt?: Date;
  }) => {
    try {
      setLoading(true);
      setError(null);

      // Validate input data
      if (!couponData.description?.trim()) {
        throw new Error('Description is required');
      }
      if (!couponData.discountPercentage || couponData.discountPercentage <= 0 || couponData.discountPercentage > 100) {
        throw new Error('Discount percentage must be between 1 and 100');
      }

      const response = await fetch('/api/admin/coupons', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(couponData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Network error' }));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (!data || typeof data !== 'object') {
        throw new Error('Invalid response format');
      }

      // Refresh coupons list
      await fetchCoupons();

      return { success: true, coupon: data.data };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create coupon';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [fetchCoupons]);

  // Update coupon
  const updateCoupon = useCallback(async (id: string, updateData: {
    description?: string;
    discountPercentage?: number;
    maxUses?: number;
    expiresAt?: Date;
    isActive?: boolean;
  }) => {
    try {
      setLoading(true);
      setError(null);

      // Validate input data
      if (!id?.trim()) {
        throw new Error('Coupon ID is required');
      }
      if (updateData.discountPercentage !== undefined && (updateData.discountPercentage <= 0 || updateData.discountPercentage > 100)) {
        throw new Error('Discount percentage must be between 1 and 100');
      }

      const response = await fetch(`/api/admin/coupons/${encodeURIComponent(id)}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Network error' }));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (!data || typeof data !== 'object') {
        throw new Error('Invalid response format');
      }

      // Refresh coupons list
      await fetchCoupons();

      return { success: true, coupon: data.data };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update coupon';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [fetchCoupons]);

  // Delete coupon
  const deleteCoupon = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);

      // Validate input data
      if (!id?.trim()) {
        throw new Error('Coupon ID is required');
      }

      const response = await fetch(`/api/admin/coupons/${encodeURIComponent(id)}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Network error' }));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      // Refresh coupons list
      await fetchCoupons();

      return { success: true };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete coupon';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [fetchCoupons]);

  return {
    coupons,
    loading,
    error,
    fetchCoupons,
    createCoupon,
    updateCoupon,
    deleteCoupon,
  };
}
