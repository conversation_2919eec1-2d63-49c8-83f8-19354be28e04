"use client";

import { motion } from "framer-motion";
import <PERSON> from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  FileText, 
  Users, 
  Wrench, 
  CheckCircle,
  ArrowRight,
  Clock,
  MessageCircle,
  Download,
  Star
} from "lucide-react";

const steps = [
  {
    step: 1,
    icon: FileText,
    title: "Place Your Order",
    description: "Fill out our simple order form with your assignment details, requirements, and deadline.",
    details: [
      "Choose your assignment type and academic level",
      "Set your deadline and page count",
      "Upload any relevant files or instructions",
      "Get an instant price quote"
    ],
    timeframe: "2-3 minutes",
    color: "text-blue-600",
    bgColor: "bg-blue-50 dark:bg-blue-950/20",
    action: "Start Order",
    actionHref: "/create-order"
  },
  {
    step: 2,
    icon: Users,
    title: "Writer Assignment",
    description: "We match your order with the most qualified writer based on subject expertise and availability.",
    details: [
      "Automatic matching with expert writers",
      "Writer reviews your requirements",
      "Direct communication channel opens",
      "Work begins immediately after confirmation"
    ],
    timeframe: "5-15 minutes",
    color: "text-green-600",
    bgColor: "bg-green-50 dark:bg-green-950/20",
    action: "View Writers",
    actionHref: "/about-us"
  },
  {
    step: 3,
    icon: Wrench,
    title: "Writing Process",
    description: "Your assigned writer researches and crafts your assignment while you can track progress in real-time.",
    details: [
      "Writer conducts thorough research",
      "Regular progress updates via dashboard",
      "Direct messaging with your writer",
      "Quality checks throughout the process"
    ],
    timeframe: "Based on deadline",
    color: "text-purple-600",
    bgColor: "bg-purple-50 dark:bg-purple-950/20",
    action: "Track Progress",
    actionHref: "/login/client"
  },
  {
    step: 4,
    icon: CheckCircle,
    title: "Delivery & Review",
    description: "Receive your completed assignment, review it, and request any revisions if needed.",
    details: [
      "Assignment delivered before deadline",
      "Plagiarism report included",
      "Free revisions if needed",
      "24/7 support for any questions"
    ],
    timeframe: "Instant delivery",
    color: "text-orange-600",
    bgColor: "bg-orange-50 dark:bg-orange-950/20",
    action: "Get Started",
    actionHref: "/create-order"
  }
];

const processFeatures = [
  {
    icon: Clock,
    title: "Real-Time Updates",
    description: "Track your order progress with live notifications"
  },
  {
    icon: MessageCircle,
    title: "Direct Communication",
    description: "Chat directly with your assigned writer anytime"
  },
  {
    icon: Download,
    title: "Instant Download",
    description: "Get your completed work delivered instantly"
  }
];

export function HowItWorksSteps() {
  return (
    <div className="container mx-auto px-4" id="steps">
      <div className="max-w-6xl mx-auto">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <Badge 
            variant="secondary" 
            className="px-4 py-2 text-sm font-medium bg-primary/10 text-primary border-primary/20 mb-4"
          >
            <Star className="w-4 h-4 mr-2" />
            Our Process
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            4 Simple Steps to Success
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Our streamlined process ensures you get high-quality academic help quickly and efficiently
          </p>
        </motion.div>

        {/* Steps Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {steps.map((step, index) => (
            <motion.div
              key={step.step}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="relative"
            >
              <Card className="h-full hover:shadow-lg transition-all duration-300 border-2 hover:border-primary/20 group">
                <CardContent className="p-6">
                  {/* Step Number Badge */}
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge 
                      variant="default" 
                      className="w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold bg-primary text-primary-foreground"
                    >
                      {step.step}
                    </Badge>
                  </div>

                  {/* Icon */}
                  <div className={`inline-flex p-3 rounded-lg ${step.bgColor} mb-4 mt-2 group-hover:scale-110 transition-transform duration-300`}>
                    <step.icon className={`w-6 h-6 ${step.color}`} />
                  </div>

                  {/* Title */}
                  <h3 className="text-xl font-semibold mb-3 group-hover:text-primary transition-colors">
                    {step.title}
                  </h3>

                  {/* Description */}
                  <p className="text-muted-foreground mb-4 leading-relaxed">
                    {step.description}
                  </p>

                  {/* Timeframe */}
                  <div className="bg-muted/50 rounded-lg p-3 mb-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Timeframe:</span>
                      <Badge variant="outline" className="text-xs">
                        {step.timeframe}
                      </Badge>
                    </div>
                  </div>

                  {/* Details List */}
                  <ul className="space-y-2 mb-4">
                    {step.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="text-sm text-muted-foreground flex items-start">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                        <span>{detail}</span>
                      </li>
                    ))}
                  </ul>

                  {/* Action Button */}
                  <Link href={step.actionHref}>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="w-full group-hover:border-primary group-hover:text-primary transition-colors"
                    >
                      {step.action}
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>

              {/* Arrow for desktop */}
              {index < steps.length - 1 && (
                <div className="hidden lg:block absolute top-1/2 -right-3 transform -translate-y-1/2 z-10">
                  <ArrowRight className="w-6 h-6 text-primary" />
                </div>
              )}
            </motion.div>
          ))}
        </div>

        {/* Process Features */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="grid md:grid-cols-3 gap-6"
        >
          {processFeatures.map((feature, index) => (
            <Card key={index} className="text-center hover:shadow-lg transition-all duration-300 border-2 hover:border-primary/20 group">
              <CardContent className="p-6">
                <div className="inline-flex p-3 rounded-lg bg-primary/10 mb-4 group-hover:bg-primary/20 transition-colors">
                  <feature.icon className="w-6 h-6 text-primary" />
                </div>
                <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                  {feature.title}
                </h3>
                <p className="text-muted-foreground text-sm">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </motion.div>
      </div>
    </div>
  );
}
