"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogTrigger, <PERSON>alogContent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Table, TableHead, TableRow, TableHeader, TableBody, TableCell } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Toolt<PERSON>, Too<PERSON>ipContent, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TooltipTrigger } from "@/components/ui/tooltip";
import { toast } from "sonner";
import { Plus, Edit, Trash2, FolderOpen, BookOpen, Info, Hash, Loader2 } from "lucide-react";

interface BlogCategory {
  id: string;
  name: string;
  description: string | null;
  slug: string;
  createdAt: string;
  updatedAt: string;
  _count: {
    blogs: number;
  };
}

interface CategoryFormState {
  name: string;
  description: string;
  slug: string;
}

const defaultCategoryForm: CategoryFormState = {
  name: "",
  description: "",
  slug: "",
};

export function CategoriesSettings() {
  const [categories, setCategories] = React.useState<BlogCategory[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [editId, setEditId] = React.useState<string | null>(null);
  const [form, setForm] = React.useState<CategoryFormState>(defaultCategoryForm);
  const [deletingId, setDeletingId] = React.useState<string | null>(null);

  React.useEffect(() => {
    fetchCategories();
  }, []);

  // Auto-generate slug from name
  React.useEffect(() => {
    if (!editId && form.name) {
      const generatedSlug = form.name
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      setForm(f => ({ ...f, slug: generatedSlug }));
    }
  }, [form.name, editId]);

  async function fetchCategories() {
    setLoading(true);
    try {
      const res = await fetch("/api/blog/categories");
      if (res.ok) {
        const data = await res.json();
        setCategories(data);
      } else {
        toast.error("Failed to fetch categories");
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      toast.error("Failed to fetch categories");
    }
    setLoading(false);
  }

  function resetForm() {
    setForm(defaultCategoryForm);
    setEditId(null);
  }

  function openEditDialog(category: BlogCategory) {
    setEditId(category.id);
    setForm({
      name: category.name,
      description: category.description || "",
      slug: category.slug,
    });
    setDialogOpen(true);
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setLoading(true);
    
    try {
      const method = editId ? "PUT" : "POST";
      const url = editId ? `/api/blog/categories/${editId}` : "/api/blog/categories";
      
      const res = await fetch(url, {
        method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...form,
          description: form.description || null,
        }),
      });

      const data = await res.json();

      if (res.ok) {
        toast.success(editId ? "Category updated successfully" : "Category created successfully");
        setDialogOpen(false);
        resetForm();
        fetchCategories();
      } else {
        toast.error(data.message || "Failed to save category");
      }
    } catch (error) {
      console.error("Error saving category:", error);
      toast.error("Failed to save category");
    }
    setLoading(false);
  }

  async function handleDelete(id: string, name: string, blogCount: number) {
    if (blogCount > 0) {
      toast.error(`Cannot delete "${name}". This category has ${blogCount} associated blog post(s).`);
      return;
    }

    setDeletingId(id);
    try {
      const res = await fetch(`/api/blog/categories/${id}`, { method: "DELETE" });
      const data = await res.json();

      if (res.ok) {
        toast.success("Category deleted successfully");
        fetchCategories();
      } else {
        toast.error(data.message || "Failed to delete category");
      }
    } catch (error) {
      console.error("Error deleting category:", error);
      toast.error("Failed to delete category");
    }
    setDeletingId(null);
  }

  return (
    <TooltipProvider>
      <Card className="w-full dark:bg-muted bg-white">
        <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="flex items-center gap-2">
            <FolderOpen className="h-5 w-5" />
            <CardTitle>Blog Categories</CardTitle>
            <Tooltip>
              <TooltipTrigger>
                <Info className="h-4 w-4 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent>
                <p>Manage blog categories for organizing posts. Categories with existing blog posts cannot be deleted.</p>
              </TooltipContent>
            </Tooltip>
          </div>
          <Dialog open={dialogOpen} onOpenChange={(open) => { setDialogOpen(open); if (!open) resetForm(); }}>
            <DialogTrigger asChild>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button onClick={() => { setEditId(null); setDialogOpen(true); }} className="w-full sm:w-auto">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Category
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Create a new blog category</p>
                </TooltipContent>
              </Tooltip>
            </DialogTrigger>
            <DialogContent className="w-[95vw] sm:max-w-md">
              <DialogHeader>
                <DialogTitle>{editId ? "Edit Category" : "Add New Category"}</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="name">Category Name *</Label>
                  <Input
                    id="name"
                    value={form.name}
                    onChange={(e) => setForm(f => ({ ...f, name: e.target.value }))}
                    placeholder="e.g., Academic Writing"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="slug">URL Slug *</Label>
                  <div className="flex items-center gap-2">
                    <Hash className="h-4 w-4 text-muted-foreground" />
                    <Input
                      id="slug"
                      value={form.slug}
                      onChange={(e) => setForm(f => ({ ...f, slug: e.target.value }))}
                      placeholder="e.g., academic-writing"
                      pattern="^[a-z0-9-]+$"
                      title="Only lowercase letters, numbers, and hyphens allowed"
                      required
                    />
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Used in URLs. Auto-generated from name, but you can customize it.
                  </p>
                </div>
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={form.description}
                    onChange={(e) => setForm(f => ({ ...f, description: e.target.value }))}
                    placeholder="Brief description of this category (optional)"
                    rows={3}
                  />
                </div>
                <DialogFooter>
                  <Button type="submit" disabled={loading}>
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {editId ? "Updating..." : "Creating..."}
                      </>
                    ) : (
                      editId ? "Update Category" : "Create Category"
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          {loading && !dialogOpen ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>Loading categories...</span>
            </div>
          ) : (
            <div className="rounded-md border overflow-x-auto">
              <Table className="min-w-[700px]">
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead className="hidden sm:table-cell">Slug</TableHead>
                    <TableHead className="hidden md:table-cell">Description</TableHead>
                    <TableHead>Blog Posts</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {categories.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                        No categories found. Create your first category to get started.
                      </TableCell>
                    </TableRow>
                  ) : (
                    categories.map((category) => (
                      <TableRow key={category.id}>
                        <TableCell className="font-medium">
                          <div className="flex flex-col">
                            <span>{category.name}</span>
                            <code className="sm:hidden text-xs bg-muted px-2 py-1 rounded mt-1 w-fit">
                              {category.slug}
                            </code>
                            <div className="md:hidden text-xs text-muted-foreground mt-1 truncate max-w-[200px]" title={category.description || ""}>
                              {category.description
                                ? (category.description.length > 30
                                    ? `${category.description.slice(0, 30)}...`
                                    : category.description)
                                : <span className="italic">No description</span>
                              }
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="hidden sm:table-cell">
                          <code className="text-xs bg-muted px-2 py-1 rounded">
                            {category.slug}
                          </code>
                        </TableCell>
                        <TableCell className="hidden md:table-cell max-w-xs">
                          <div className="truncate" title={category.description || ""}>
                            {category.description
                              ? (category.description.length > 50
                                  ? `${category.description.slice(0, 50)}...`
                                  : category.description)
                              : <span className="text-muted-foreground italic">No description</span>
                            }
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary" className="flex items-center gap-1 w-fit">
                            <BookOpen className="h-3 w-3" />
                            {category._count.blogs}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-end gap-1 sm:gap-2">
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => openEditDialog(category)}
                                  className="w-full sm:w-auto"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Edit category details</p>
                              </TooltipContent>
                            </Tooltip>
                            <AlertDialog>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <AlertDialogTrigger asChild>
                                    <Button
                                      size="sm"
                                      variant="destructive"
                                      disabled={category._count.blogs > 0}
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </AlertDialogTrigger>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>
                                    {category._count.blogs > 0
                                      ? `Cannot delete - has ${category._count.blogs} blog post(s)`
                                      : "Delete category"
                                    }
                                  </p>
                                </TooltipContent>
                              </Tooltip>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Delete Category</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to delete <strong>{category.name}</strong>?
                                    This action cannot be undone and will remove all associated data.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel disabled={deletingId === category.id}>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    className="bg-destructive hover:bg-destructive/90"
                                    onClick={() => handleDelete(category.id, category.name, category._count.blogs)}
                                    disabled={deletingId === category.id}
                                  >
                                    {deletingId === category.id ? (
                                      <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Deleting...
                                      </>
                                    ) : (
                                      "Delete Category"
                                    )}
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </TooltipProvider>
  );
}
