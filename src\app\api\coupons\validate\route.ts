import { NextRequest, NextResponse } from "next/server";
import { apiSuccess, apiError, getCurrentUser } from "@/lib/api-utils";
import { couponService } from "@/lib/coupon-service";
import { couponValidationSchema } from "@/lib/validations";

// Validate coupon code
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    console.log("🔍 [COUPON VALIDATE] Request received");

    // Get current user
    const user = await getCurrentUser();
    console.log("🔍 [COUPON VALIDATE] Current user:", user ? { id: user.id, role: user.role, email: user.email } : "null");

    if (!user) {
      console.log("❌ [COUPON VALIDATE] No authenticated user");
      return apiError("Authentication required", 401);
    }

    const body = await req.json();
    console.log("🔍 [COUPON VALIDATE] Request body:", JSON.stringify(body, null, 2));

    const parsed = couponValidationSchema.safeParse(body);

    if (!parsed.success) {
      console.log("❌ [COUPON VALIDATE] Validation failed:", parsed.error.flatten().fieldErrors);
      console.log("❌ [COUPON VALIDATE] Raw validation errors:", parsed.error.errors);
      return apiError("Invalid input data", 400, parsed.error.flatten().fieldErrors);
    }

    const { code, originalPrice } = parsed.data;
    console.log("🔍 [COUPON VALIDATE] Parsed data - Code:", code, "Original Price:", originalPrice, "User ID:", user.id);

    const result = await couponService.validateCoupon(code, user.id, originalPrice);
    console.log("🔍 [COUPON VALIDATE] Service result:", JSON.stringify(result, null, 2));

    if (!result.isValid) {
      console.log("❌ [COUPON VALIDATE] Coupon invalid:", result.error);
      return apiError(result.error || "Invalid coupon", 400);
    }

    console.log("✅ [COUPON VALIDATE] Coupon valid, returning success");
    return apiSuccess({
      coupon: result.coupon,
      discountAmount: result.discountAmount,
      finalPrice: result.finalPrice,
    });
  } catch (error) {
    console.error("❌ [COUPON VALIDATE] Unexpected error:", error);
    console.error("❌ [COUPON VALIDATE] Error stack:", error instanceof Error ? error.stack : "No stack trace");
    return apiError("Failed to validate coupon", 500);
  }
}
