'use client';

import { usePathname } from 'next/navigation';
import { Navbar } from "@/components/layout/navbar";
import { DynamicBreadcrumbs } from "@/components/layout/DynamicBreadcrumbs";
import { Footer } from "@/components/footer/Footer";
import BackToTop from "@/components/common/back-to-top";

//Custom labels for specific breadcrumbs paths
const pathMap = {
  about: "About Us",
  contact: "Contact Us",
  services: "Our Services",
  blog: "Blog",
  login: "Sign In",
  faqs: "Frequently Asked Questions",
  //add more custom labels as needed
};

//Exclude paths that shouldn't appear in breadcrums
const excludePaths = [
  "api",
  "_next",
  "static",
  "public",
  "favicon.ico",
  "robots.txt",
  "sitemap.xml",
  "images",
  "fonts",
  "styles",
  "scripts",
  "assets",
];

export default function WithFooterLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  
  // Check if we're on a blog detail page
  const isBlogDetailPage = pathname?.match(/^\/blog\/[^/]+$/i);
  
  return (
    <>
      <Navbar />
      {!isBlogDetailPage && (
        <div className="ml-2 md:ml-8">
          <DynamicBreadcrumbs pathMap={pathMap} excludePaths={excludePaths} />
        </div>
      )}
      {children}
      <Footer />
      {/* Back to top button - positioned above Tawk.to chat widget */}
      <BackToTop />
    </>
  );
}
