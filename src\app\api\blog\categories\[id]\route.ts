import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { z } from "zod";
import { checkPermission, apiSuccess, apiError } from "@/lib/api-utils";

// Validation schema for blog category
const BlogCategorySchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(100, "Name must be less than 100 characters"),
  description: z.string().optional().nullable(),
  slug: z.string().min(2, "Slug must be at least 2 characters").max(100, "Slug must be less than 100 characters")
    .regex(/^[a-z0-9-]+$/, "Slug must contain only lowercase letters, numbers, and hyphens"),
});

export async function GET(
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    const category = await prisma.blogCategory.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            blogs: true,
          },
        },
      },
    });

    if (!category) {
      return apiError("Category not found", 404);
    }

    return NextResponse.json(category);
  } catch (error) {
    console.error("Error fetching category:", error);
    return apiError("Failed to fetch category", 500);
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check admin permission
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    const { id } = await params;
    const body = await req.json();
    const parsed = BlogCategorySchema.safeParse(body);
    
    if (!parsed.success) {
      return apiError("Invalid input data", 400, parsed.error.flatten().fieldErrors);
    }

    const { name, description, slug } = parsed.data;

    // Check if category exists
    const existingCategory = await prisma.blogCategory.findUnique({
      where: { id },
    });

    if (!existingCategory) {
      return apiError("Category not found", 404);
    }

    // Check if another category with same name or slug exists (excluding current category)
    const duplicateCategory = await prisma.blogCategory.findFirst({
      where: {
        OR: [
          { name },
          { slug },
        ],
        id: { not: id },
      },
    });

    if (duplicateCategory) {
      if (duplicateCategory.name === name) {
        return apiError("Category with this name already exists", 409);
      }
      if (duplicateCategory.slug === slug) {
        return apiError("Category with this slug already exists", 409);
      }
    }

    const updatedCategory = await prisma.blogCategory.update({
      where: { id },
      data: {
        name,
        description,
        slug,
      },
    });

    return apiSuccess(updatedCategory, "Category updated successfully");
  } catch (error) {
    console.error("Error updating category:", error);
    return apiError("Failed to update category", 500);
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check admin permission
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    const { id } = await params;

    // Check if category exists
    const existingCategory = await prisma.blogCategory.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            blogs: true,
          },
        },
      },
    });

    if (!existingCategory) {
      return apiError("Category not found", 404);
    }

    // Check if category has associated blogs
    if (existingCategory._count.blogs > 0) {
      return apiError(
        `Cannot delete category. This category has ${existingCategory._count.blogs} associated blog post(s). Please reassign or delete the blog posts first.`,
        409
      );
    }

    await prisma.blogCategory.delete({
      where: { id },
    });

    return apiSuccess(null, "Category deleted successfully");
  } catch (error) {
    console.error("Error deleting category:", error);
    return apiError("Failed to delete category", 500);
  }
}
