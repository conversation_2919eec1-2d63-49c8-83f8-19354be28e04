import { NextRequest, NextResponse } from "next/server";
import { apiSuccess, apiError, getCurrentUser } from "@/lib/api-utils";
import { couponService } from "@/lib/coupon-service";
import { couponApplicationSchema } from "@/lib/validations";

// Apply coupon code
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Get current user
    const user = await getCurrentUser();
    if (!user) {
      return apiError("Authentication required", 401);
    }

    const body = await req.json();
    const parsed = couponApplicationSchema.safeParse(body);
    
    if (!parsed.success) {
      return apiError("Invalid input data", 400, parsed.error.flatten().fieldErrors);
    }

    const { code, assignmentId, originalPrice } = parsed.data;

    const result = await couponService.applyCoupon(code, user.id, originalPrice, assignmentId);

    if (!result.success) {
      return apiError(result.error || "Failed to apply coupon", 400);
    }

    return apiSuccess({
      couponUsageId: result.couponUsageId,
      discountAmount: result.discountAmount,
      finalPrice: result.finalPrice,
    }, "Coupon applied successfully");
  } catch (error) {
    console.error("Error applying coupon:", error);
    return apiError("Failed to apply coupon", 500);
  }
}
