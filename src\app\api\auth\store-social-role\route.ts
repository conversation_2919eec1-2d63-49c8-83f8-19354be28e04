import { NextRequest, NextResponse } from "next/server";
import { storeSocialLoginRole } from "@/lib/social-login-role-store";
import { UserRole } from "@prisma/client";

export async function POST(req: NextRequest) {
  try {
    const { email, intendedRole } = await req.json();

    if (!email || !intendedRole) {
      return NextResponse.json(
        { error: "Email and intended role are required" },
        { status: 400 }
      );
    }

    // Validate role
    if (!["ADMIN", "WRITER", "CLIENT"].includes(intendedRole)) {
      return NextResponse.json(
        { error: "Invalid role" },
        { status: 400 }
      );
    }

    // Store the intended role for this email
    storeSocialLoginRole(email, intendedRole as UserRole);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error storing social login role:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
