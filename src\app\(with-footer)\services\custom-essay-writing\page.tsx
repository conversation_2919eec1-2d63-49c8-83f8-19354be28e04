import React from "react";
import { <PERSON>ada<PERSON> } from "next";
import { getRouteMetadata } from "@/lib/route-metadata";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
// import { Separator } from '@/components/ui/separator';
import {
  CheckCircle,
  Clock,
  Users,
  BookOpen,
  PenTool,
  Target,
  Star,
  Award,
} from "lucide-react";

export async function generateMetadata(): Promise<Metadata> {
  return await getRouteMetadata("/services/custom-essay-writing");
}

const CustomEssayWriting: React.FC = () => {
  const features = [
    {
      icon: <PenTool className="h-5 w-5" />,
      title: "Original Content",
      description: "100% plagiarism-free essays written from scratch",
    },
    {
      icon: <Clock className="h-5 w-5" />,
      title: "Timely Delivery",
      description: "Meet your deadlines with our reliable turnaround times",
    },
    {
      icon: <Users className="h-5 w-5" />,
      title: "Expert Writers",
      description: "PhD and Master&apos;s level writers in your field",
    },
    {
      icon: <Target className="h-5 w-5" />,
      title: "Custom Requirements",
      description: "Tailored to your specific guidelines and requirements",
    },
  ];

  const essayTypes = [
    "Argumentative Essays",
    "Analytical Essays",
    "Descriptive Essays",
    "Narrative Essays",
    "Expository Essays",
    "Compare & Contrast",
    "Cause & Effect",
    "Research Papers",
  ];

  const qualityStandards = [
    "Thorough research using credible sources",
    "Proper citation in APA, MLA, Chicago, or Harvard",
    "Clear thesis statement and logical structure",
    "Grammar and style perfection",
    "Unlimited revisions until satisfied",
    "Confidentiality guaranteed",
  ];

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-12 max-w-6xl">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <Badge variant="secondary" className="mb-4">
            <Star className="h-4 w-4 mr-2" />
            Most Popular Service
          </Badge>
          <h1 className="text-4xl font-bold text-foreground mb-4">
            Custom Essay Writing Services
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Professional academic essay writing that meets your exact
            requirements. Our expert writers craft original, high-quality essays
            tailored to your academic level and subject area.
          </p>
        </div>

        {/* What is Custom Essay Writing */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-6 w-6 text-primary" />
              What is Custom Essay Writing?
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground leading-relaxed mb-4">
              Custom essay writing is a personalized academic service where
              professional writers create original essays based on your specific
              requirements, topic, and academic guidelines. Unlike pre-written
              essays, custom essays are crafted from scratch to match your
              unique needs, ensuring originality and relevance to your
              assignment.
            </p>
            <p className="text-muted-foreground leading-relaxed">
              Our custom essay writing service covers all academic levels from
              high school to PhD, across various disciplines including
              literature, history, psychology, business, sciences, and more.
              Each essay is meticulously researched, properly structured, and
              formatted according to your required citation style.
            </p>
          </CardContent>
        </Card>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {features.map((feature, index) => (
            <Card
              key={index}
              className="text-center hover:shadow-lg transition-shadow"
            >
              <CardContent className="pt-6">
                <div className="text-primary mb-3 flex justify-center">
                  {feature.icon}
                </div>
                <h3 className="font-semibold mb-2">{feature.title}</h3>
                <p className="text-sm text-muted-foreground">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Two Column Layout */}
        <div className="grid lg:grid-cols-2 gap-8 mb-12">
          {/* Essay Types */}
          <Card>
            <CardHeader>
              <CardTitle>Essay Types We Cover</CardTitle>
              <CardDescription>
                Comprehensive coverage of all academic essay formats
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-2">
                {essayTypes.map((type, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                    <span className="text-sm">{type}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quality Standards */}
          <Card>
            <CardHeader>
              <CardTitle>Our Quality Standards</CardTitle>
              <CardDescription>
                What you can expect from every custom essay
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {qualityStandards.map((standard, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-primary flex-shrink-0 mt-0.5" />
                    <span className="text-sm">{standard}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Why Choose Us */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-6 w-6 text-primary" />
              Why Choose Our Custom Essay Writing Service?
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-3">Academic Excellence</h4>
                <p className="text-muted-foreground text-sm mb-4">
                  Our writers hold advanced degrees from prestigious
                  universities and have years of experience in academic writing.
                  They understand the nuances of different citation styles,
                  academic tone, and research methodologies required for
                  high-quality essays.
                </p>
                <h4 className="font-semibold mb-3">Personalized Approach</h4>
                <p className="text-muted-foreground text-sm">
                  We don&apos;t believe in one-size-fits-all solutions. Every
                  essay is crafted according to your specific instructions,
                  academic level, and professor&apos;s requirements, ensuring
                  you receive work that truly represents your academic voice.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-3">Transparent Process</h4>
                <p className="text-muted-foreground text-sm mb-4">
                  From the moment you place your order, you can track progress,
                  communicate directly with your writer, and request updates.
                  Our platform ensures complete transparency throughout the
                  writing process.
                </p>
                <h4 className="font-semibold mb-3">Satisfaction Guarantee</h4>
                <p className="text-muted-foreground text-sm">
                  We stand behind our work with unlimited revisions and a
                  money-back guarantee. Your satisfaction is our priority, and
                  we&apos;ll work with you until you&apos;re completely happy
                  with your essay.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* CTA Section */}
        <Card className="text-center bg-primary/5 border-primary/20">
          <CardContent className="pt-6">
            <h3 className="text-2xl font-bold mb-4">Ready to Get Started?</h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Join thousands of satisfied students who have improved their
              grades with our custom essay writing service. Post your assignment
              today and connect with expert writers ready to help you succeed.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="px-8">
                Post Your Assignment
              </Button>
              <Button size="lg" variant="outline" className="px-8">
                View Sample Essays
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CustomEssayWriting;
