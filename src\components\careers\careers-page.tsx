"use client";

import { motion } from "framer-motion";
import { CareersHero } from "./careers-hero";
import { CareersBenefits } from "./careers-benefits";
import { CareersEarnings } from "./careers-earnings";
import { CareersRequirements } from "./careers-requirements";
import { CareersProcess } from "./careers-process";
import { CareersTestimonials } from "./careers-testimonials";
import { CareersCTA } from "./careers-cta";

export function CareersPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Hero Section */}
      <CareersHero />

      {/* Benefits Section */}
      <motion.section
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="py-16"
      >
        <CareersBenefits />
      </motion.section>

      {/* Earnings Section */}
      <motion.section
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="py-16 bg-muted/30"
      >
        <CareersEarnings />
      </motion.section>

      {/* Requirements Section */}
      <motion.section
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="py-16"
      >
        <CareersRequirements />
      </motion.section>

      {/* Application Process Section */}
      <motion.section
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="py-16 bg-muted/30"
      >
        <CareersProcess />
      </motion.section>

      {/* Writer Testimonials Section */}
      <motion.section
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="py-16"
      >
        <CareersTestimonials />
      </motion.section>

      {/* Call to Action Section */}
      <motion.section
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="py-16 bg-muted/30"
      >
        <CareersCTA />
      </motion.section>
    </div>
  );
}
