// src/app/api/admin/company-info/route.ts
import { NextRequest } from "next/server";
import prisma from "@/lib/prisma";
import {
  apiSuccess,
  apiError,
  parseRequestBody,
  checkPermission,
  isApiError,
} from "@/lib/api-utils";
import { companyInfoCreateSchema, companyInfoUpdateSchema } from "@/lib/validations";
import type { CompanyInfoResponse, CompanyInfoCreateData, CompanyInfoUpdateData } from "@/types/api";
import { UserRole } from "@prisma/client";

// GET /api/admin/company-info - Get company information
export async function GET() {
  try {
    // Get the first (and should be only) company info record
    const companyInfo = await prisma.companyInfo.findFirst();

    if (!companyInfo) {
      return apiError("Company information not found", 404);
    }

    const response: CompanyInfoResponse = {
      id: companyInfo.id,
      companyName: companyInfo.companyName,
      address: companyInfo.address,
      city: companyInfo.city,
      state: companyInfo.state,
      zipCode: companyInfo.zipCode,
      country: companyInfo.country,
      phone: companyInfo.phone,
      tollFreePhone: companyInfo.tollFreePhone,
      internationalPhone: companyInfo.internationalPhone,
      supportEmail: companyInfo.supportEmail,
      inquiriesEmail: companyInfo.inquiriesEmail,
      businessHours: companyInfo.businessHours,
      description: companyInfo.description,
      website: companyInfo.website,
      createdAt: companyInfo.createdAt.toISOString(),
      updatedAt: companyInfo.updatedAt.toISOString(),
    };

    return apiSuccess(response, "Company information retrieved successfully");
  } catch (error) {
    console.error("Error fetching company info:", error);
    return apiError("Failed to fetch company information", 500);
  }
}

// POST /api/admin/company-info - Create company information
export async function POST(request: NextRequest) {
  try {
    // Check admin permission
    const permissionCheck = await checkPermission([UserRole.ADMIN]);
    if (permissionCheck) {
      return permissionCheck;
    }

    // Parse and validate request body
    const parseResult = await parseRequestBody<CompanyInfoCreateData>(
      request,
      companyInfoCreateSchema
    );

    if (isApiError(parseResult)) {
      return apiError(parseResult.message, 400, parseResult.errors);
    }

    const data = parseResult;

    // Check if company info already exists
    const existingCompanyInfo = await prisma.companyInfo.findFirst();
    if (existingCompanyInfo) {
      return apiError("Company information already exists. Use PUT to update.", 409);
    }

    // Create company info
    const companyInfo = await prisma.companyInfo.create({
      data: {
        companyName: data.companyName,
        address: data.address,
        city: data.city,
        state: data.state,
        zipCode: data.zipCode,
        country: data.country,
        phone: data.phone,
        tollFreePhone: data.tollFreePhone,
        internationalPhone: data.internationalPhone,
        supportEmail: data.supportEmail,
        inquiriesEmail: data.inquiriesEmail,
        businessHours: data.businessHours,
        description: data.description,
        website: data.website,
      },
    });

    const response: CompanyInfoResponse = {
      id: companyInfo.id,
      companyName: companyInfo.companyName,
      address: companyInfo.address,
      city: companyInfo.city,
      state: companyInfo.state,
      zipCode: companyInfo.zipCode,
      country: companyInfo.country,
      phone: companyInfo.phone,
      tollFreePhone: companyInfo.tollFreePhone,
      internationalPhone: companyInfo.internationalPhone,
      supportEmail: companyInfo.supportEmail,
      inquiriesEmail: companyInfo.inquiriesEmail,
      businessHours: companyInfo.businessHours,
      description: companyInfo.description,
      website: companyInfo.website,
      createdAt: companyInfo.createdAt.toISOString(),
      updatedAt: companyInfo.updatedAt.toISOString(),
    };

    return apiSuccess(response, "Company information created successfully");
  } catch (error) {
    console.error("Error creating company info:", error);
    return apiError("Failed to create company information", 500);
  }
}

// PUT /api/admin/company-info - Update company information
export async function PUT(request: NextRequest) {
  try {
    // Check admin permission
    const permissionCheck = await checkPermission([UserRole.ADMIN]);
    if (permissionCheck) {
      return permissionCheck;
    }

    // Parse and validate request body
    const parseResult = await parseRequestBody<CompanyInfoUpdateData>(
      request,
      companyInfoUpdateSchema
    );

    if (isApiError(parseResult)) {
      return apiError(parseResult.message, 400, parseResult.errors);
    }

    const data = parseResult;

    // Get existing company info
    const existingCompanyInfo = await prisma.companyInfo.findFirst();
    if (!existingCompanyInfo) {
      return apiError("Company information not found", 404);
    }

    // Update company info
    const companyInfo = await prisma.companyInfo.update({
      where: { id: existingCompanyInfo.id },
      data: {
        ...(data.companyName !== undefined && { companyName: data.companyName }),
        ...(data.address !== undefined && { address: data.address }),
        ...(data.city !== undefined && { city: data.city }),
        ...(data.state !== undefined && { state: data.state }),
        ...(data.zipCode !== undefined && { zipCode: data.zipCode }),
        ...(data.country !== undefined && { country: data.country }),
        ...(data.phone !== undefined && { phone: data.phone }),
        ...(data.tollFreePhone !== undefined && { tollFreePhone: data.tollFreePhone }),
        ...(data.internationalPhone !== undefined && { internationalPhone: data.internationalPhone }),
        ...(data.supportEmail !== undefined && { supportEmail: data.supportEmail }),
        ...(data.inquiriesEmail !== undefined && { inquiriesEmail: data.inquiriesEmail }),
        ...(data.businessHours !== undefined && { businessHours: data.businessHours }),
        ...(data.description !== undefined && { description: data.description }),
        ...(data.website !== undefined && { website: data.website }),
      },
    });

    const response: CompanyInfoResponse = {
      id: companyInfo.id,
      companyName: companyInfo.companyName,
      address: companyInfo.address,
      city: companyInfo.city,
      state: companyInfo.state,
      zipCode: companyInfo.zipCode,
      country: companyInfo.country,
      phone: companyInfo.phone,
      tollFreePhone: companyInfo.tollFreePhone,
      internationalPhone: companyInfo.internationalPhone,
      supportEmail: companyInfo.supportEmail,
      inquiriesEmail: companyInfo.inquiriesEmail,
      businessHours: companyInfo.businessHours,
      description: companyInfo.description,
      website: companyInfo.website,
      createdAt: companyInfo.createdAt.toISOString(),
      updatedAt: companyInfo.updatedAt.toISOString(),
    };

    return apiSuccess(response, "Company information updated successfully");
  } catch (error) {
    console.error("Error updating company info:", error);
    return apiError("Failed to update company information", 500);
  }
}
