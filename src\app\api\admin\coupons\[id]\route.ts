import { NextRequest, NextResponse } from "next/server";
import { apiSuccess, apiError, checkPermission } from "@/lib/api-utils";
import { couponService } from "@/lib/coupon-service";
import { couponUpdateSchema } from "@/lib/validations";

// Update coupon (Admin only)
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Only admins can update coupons
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    const resolvedParams = await params;
    const body = await req.json();
    const parsed = couponUpdateSchema.safeParse(body);

    if (!parsed.success) {
      return apiError("Invalid input data", 400, parsed.error.flatten().fieldErrors);
    }

    const result = await couponService.updateCoupon(resolvedParams.id, parsed.data);

    if (!result.success) {
      return apiError(result.error || "Failed to update coupon", 500);
    }

    return apiSuccess(result.coupon, "Coupon updated successfully");
  } catch (error) {
    console.error("Error updating coupon:", error);
    return apiError("Failed to update coupon", 500);
  }
}

// Delete coupon (Admin only)
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Only admins can delete coupons
    const permissionError = await checkPermission(["ADMIN"]);
    if (permissionError) return permissionError;

    const resolvedParams = await params;
    const result = await couponService.deleteCoupon(resolvedParams.id);

    if (!result.success) {
      return apiError(result.error || "Failed to delete coupon", 500);
    }

    return apiSuccess(null, "Coupon deleted successfully");
  } catch (error) {
    console.error("Error deleting coupon:", error);
    return apiError("Failed to delete coupon", 500);
  }
}
