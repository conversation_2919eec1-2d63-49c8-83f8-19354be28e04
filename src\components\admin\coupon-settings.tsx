"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { toast } from "sonner";
import { useAdminCoupons } from "@/hooks/use-coupon";
import {
  Plus,
  Edit,
  Trash2,
  Copy,
  Calendar as CalendarIcon,
  RefreshCw,
  Ticket,
  Users,
  TrendingUp,
  AlertCircle,
} from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

interface CouponFormData {
  description: string;
  discountPercentage: number;
  maxUses?: number;
  expiresAt?: Date;
}

interface CouponData {
  id: string;
  code: string;
  description: string;
  discountPercentage: number;
  isActive: boolean;
  maxUses: number | null;
  currentUses: number;
  expiresAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
  usageCount: number;
}

const defaultFormData: CouponFormData = {
  description: "",
  discountPercentage: 10,
  maxUses: undefined,
  expiresAt: undefined,
};

export function CouponSettings() {
  const {
    coupons,
    loading,
    error,
    fetchCoupons,
    createCoupon,
    updateCoupon,
    deleteCoupon,
  } = useAdminCoupons();

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingCoupon, setEditingCoupon] = useState<CouponData | null>(null);
  const [formData, setFormData] = useState<CouponFormData>(defaultFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch coupons on component mount
  useEffect(() => {
    fetchCoupons();
  }, [fetchCoupons]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Client-side validation
      if (!formData.description?.trim()) {
        toast.error("Description is required");
        return;
      }

      if (!formData.discountPercentage || formData.discountPercentage < 1 || formData.discountPercentage > 100) {
        toast.error("Discount percentage must be between 1 and 100");
        return;
      }

      console.log("Submitting coupon data:", formData);

      let result;
      if (editingCoupon) {
        result = await updateCoupon(editingCoupon.id, formData);
      } else {
        result = await createCoupon(formData);
      }

      if (result.success) {
        toast.success(editingCoupon ? "Coupon updated successfully" : "Coupon created successfully");
        setIsCreateDialogOpen(false);
        setEditingCoupon(null);
        setFormData(defaultFormData);
      } else {
        toast.error(result.error || "Failed to save coupon");
      }
    } catch (error) {
      console.error("Coupon form submission error:", error);
      toast.error("An unexpected error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle edit
  const handleEdit = (coupon: CouponData) => {
    setEditingCoupon(coupon);
    setFormData({
      description: coupon.description,
      discountPercentage: coupon.discountPercentage,
      maxUses: coupon.maxUses || undefined,
      expiresAt: coupon.expiresAt ? new Date(coupon.expiresAt) : undefined,
    });
    setIsCreateDialogOpen(true);
  };

  // Handle delete
  const handleDelete = async (couponId: string) => {
    const result = await deleteCoupon(couponId);
    if (result.success) {
      toast.success("Coupon deleted successfully");
    } else {
      toast.error(result.error || "Failed to delete coupon");
    }
  };

  // Handle toggle active status
  const handleToggleActive = async (coupon: CouponData) => {
    const result = await updateCoupon(coupon.id, {
      isActive: !coupon.isActive,
    });

    if (result.success) {
      toast.success(`Coupon ${coupon.isActive ? 'deactivated' : 'activated'} successfully`);
    } else {
      toast.error(result.error || "Failed to update coupon status");
    }
  };

  // Copy coupon code to clipboard
  const copyToClipboard = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      toast.success("Coupon code copied to clipboard");
    } catch (error) {
      console.error("Clipboard copy error:", error);
      toast.error("Failed to copy coupon code");
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData(defaultFormData);
    setEditingCoupon(null);
  };

  // Calculate statistics
  const totalCoupons = coupons.length;
  const activeCoupons = coupons.filter(c => c.isActive).length;
  const totalUsages = coupons.reduce((sum, c) => sum + (c.usageCount || 0), 0);

  if (loading && coupons.length === 0) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin" />
        <span className="ml-2">Loading coupons...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-xl sm:text-2xl font-bold tracking-tight">Coupon Management</h2>
          <p className="text-muted-foreground text-sm sm:text-base">
            Create and manage discount coupons for your platform
          </p>
        </div>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 w-full sm:w-auto">
          <Button onClick={fetchCoupons} variant="outline" size="sm" className="w-full sm:w-auto">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={(open) => {
            setIsCreateDialogOpen(open);
            if (!open) resetForm();
          }}>
            <DialogTrigger asChild>
              <Button className="w-full sm:w-auto">
                <Plus className="h-4 w-4 mr-2" />
                Create Coupon
              </Button>
            </DialogTrigger>
            <DialogContent className="w-[95vw] sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>
                  {editingCoupon ? "Edit Coupon" : "Create New Coupon"}
                </DialogTitle>
                <DialogDescription>
                  {editingCoupon 
                    ? "Update the coupon details below."
                    : "Create a new discount coupon. A unique code will be generated automatically."
                  }
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="e.g., New customer discount, Holiday special..."
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="discountPercentage">Discount Percentage</Label>
                  <Input
                    id="discountPercentage"
                    type="number"
                    min="1"
                    max="100"
                    placeholder="10"
                    value={formData.discountPercentage}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      discountPercentage: parseInt(e.target.value) || 10
                    }))}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxUses">Maximum Uses (Optional)</Label>
                  <Input
                    id="maxUses"
                    type="number"
                    min="1"
                    placeholder="Leave empty for unlimited uses"
                    value={formData.maxUses || ""}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      maxUses: e.target.value ? parseInt(e.target.value) : undefined 
                    }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Expiration Date (Optional)</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !formData.expiresAt && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.expiresAt ? format(formData.expiresAt, "PPP") : "No expiration"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={formData.expiresAt}
                        onSelect={(date) => setFormData(prev => ({ ...prev, expiresAt: date }))}
                        disabled={(date) => date < new Date()}
                        initialFocus
                      />
                      {formData.expiresAt && (
                        <div className="p-3 border-t">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setFormData(prev => ({ ...prev, expiresAt: undefined }))}
                            className="w-full"
                          >
                            Clear Date
                          </Button>
                        </div>
                      )}
                    </PopoverContent>
                  </Popover>
                </div>

                <DialogFooter>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        {editingCoupon ? "Updating..." : "Creating..."}
                      </>
                    ) : (
                      editingCoupon ? "Update Coupon" : "Create Coupon"
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Coupons</CardTitle>
            <Ticket className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCoupons}</div>
            <p className="text-xs text-muted-foreground">
              {activeCoupons} active
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Usage</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalUsages}</div>
            <p className="text-xs text-muted-foreground">
              Across all coupons
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {totalCoupons > 0 ? Math.round((activeCoupons / totalCoupons) * 100) : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              Of total coupons
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Error Display */}
      {error && (
        <div className="flex items-center gap-2 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
          <AlertCircle className="h-4 w-4 text-destructive" />
          <span className="text-sm text-destructive">{error}</span>
        </div>
      )}

      {/* Coupons Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Coupons</CardTitle>
        </CardHeader>
        <CardContent>
          {coupons.length === 0 ? (
            <div className="text-center py-8">
              <Ticket className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No coupons yet</h3>
              <p className="text-muted-foreground mb-4">
                Create your first coupon to start offering discounts to customers.
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table className="min-w-[800px]">
                <TableHeader>
                  <TableRow>
                    <TableHead>Code</TableHead>
                    <TableHead className="hidden sm:table-cell">Description</TableHead>
                    <TableHead>Discount</TableHead>
                    <TableHead className="hidden md:table-cell">Usage</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="hidden lg:table-cell">Expires</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {coupons.map((coupon) => (
                    <TableRow key={coupon.id}>
                      <TableCell>
                        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-1 sm:gap-2">
                          <code className="bg-muted px-2 py-1 rounded text-xs sm:text-sm font-mono">
                            {coupon.code}
                          </code>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyToClipboard(coupon.code)}
                            className="h-6 w-6 p-0 sm:h-8 sm:w-8"
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                      <TableCell className="hidden sm:table-cell max-w-[200px] truncate" title={coupon.description}>
                        {coupon.description}
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary" className="text-xs">
                          {coupon.discountPercentage}% OFF
                        </Badge>
                      </TableCell>
                      <TableCell className="hidden md:table-cell">
                        <span className="text-sm">
                          {coupon.usageCount || 0}
                          {coupon.maxUses && ` / ${coupon.maxUses}`}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-1 sm:gap-2">
                          <Switch
                            checked={coupon.isActive}
                            onCheckedChange={() => handleToggleActive(coupon)}
                          />
                          <Badge variant={coupon.isActive ? "default" : "secondary"} className="text-xs">
                            {coupon.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell className="hidden lg:table-cell">
                        {coupon.expiresAt ? (
                          <span className="text-sm">
                            {format(new Date(coupon.expiresAt), "MMM dd, yyyy")}
                          </span>
                        ) : (
                          <span className="text-sm text-muted-foreground">Never</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(coupon)}
                            className="w-full sm:w-auto"
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="sm" className="w-full sm:w-auto">
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Delete Coupon</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete this coupon? This action cannot be undone.
                                  Users who have already used this coupon will not be affected.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDelete(coupon.id)}
                                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
