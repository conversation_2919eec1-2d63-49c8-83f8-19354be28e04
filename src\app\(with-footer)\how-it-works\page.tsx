import { Suspense } from "react";
import { <PERSON>ada<PERSON> } from "next";
import { Skeleton } from "@/components/ui/skeleton";
import { HowItWorksPage } from "@/components/how-it-works/how-it-works-page";
import { generateDynamicMetadata } from "@/lib/metadata-utils";

export async function generateMetadata(): Promise<Metadata> {
  return await generateDynamicMetadata({
    title: "How It Works - Simple Academic Writing Process",
    description: "Learn how our simple 4-step process works. From placing your order to receiving your completed assignment, we make academic writing help easy and transparent.",
    keywords: [
      "how it works",
      "academic writing process",
      "order process",
      "assignment help steps",
      "writing service guide",
      "homework help process",
      "essay writing steps",
      "student guide",
      "academic support",
      "writing assistance"
    ],
    path: "/how-it-works",
    ogImage: "/images/how-it-works-og.jpg",
  });
}

function HowItWorksSkeleton() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto space-y-8">
          {/* Hero Section Skeleton */}
          <div className="text-center space-y-4">
            <Skeleton className="h-16 w-96 mx-auto" />
            <Skeleton className="h-6 w-[600px] mx-auto" />
          </div>

          {/* Steps Section Skeleton */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="space-y-4">
                <Skeleton className="h-48 w-full rounded-lg" />
              </div>
            ))}
          </div>

          {/* Features Section Skeleton */}
          <div className="grid md:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} className="h-32 w-full rounded-lg" />
            ))}
          </div>

          {/* FAQ Section Skeleton */}
          <div className="space-y-4">
            <Skeleton className="h-8 w-48 mx-auto" />
            {[1, 2, 3, 4, 5].map((i) => (
              <Skeleton key={i} className="h-16 w-full rounded-lg" />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function HowItWorks() {
  return (
    <Suspense fallback={<HowItWorksSkeleton />}>
      <HowItWorksPage />
    </Suspense>
  );
}
